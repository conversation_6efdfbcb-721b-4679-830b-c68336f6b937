<?php
// 数据库配置
define('DB_HOST', 'mail.xoxome.online');
define('DB_NAME', 'timezone');
define('DB_USER', 'timezone');
define('DB_PASS', 'M3Nbnni46HndHhzE');

// API配置
define('API_SECRET', 'timezone_spoofer_secret_2024');
define('JWT_SECRET', 'jwt_secret_key_timezone_2024');

// CORS设置 - 允许所有域名访问
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-Custom-Header');
header('Access-Control-Allow-Credentials: false');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 数据库连接函数
function getDBConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        return null;
    }
}

// 激活码数据库连接函数
function getActivationDBConnection() {
    try {
        // 获取xoxome.online的IP地址
        $activationHost = gethostbyname('xoxome.online');

        $pdo = new PDO(
            "mysql:host=" . $activationHost . ";dbname=jihuoma;charset=utf8mb4",
            'jihuoma',
            '123123',
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        error_log("Activation database connection failed: " . $e->getMessage());
        return null;
    }
}

// 响应函数
function sendResponse($success, $message, $data = null, $code = 200) {
    http_response_code($code);
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
    exit();
}

// 生成设备指纹 (已废弃 - 不再使用设备限制)
function generateDeviceFingerprint($userAgent = '', $additionalData = '') {
    return null; // 不再生成设备指纹
}

// 密码哈希
function hashPassword($password) {
    return password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
}

// 验证密码
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// 获取客户端IP
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            return trim($ips[0]);
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

// 记录登录日志
function logLogin($pdo, $userId, $deviceFingerprint = null, $status = 'success') {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO login_logs (user_id, device_fingerprint, ip_address, user_agent, status)
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $userId,
            null, // 不再记录设备指纹
            getClientIP(),
            $_SERVER['HTTP_USER_AGENT'] ?? '',
            $status
        ]);
    } catch (Exception $e) {
        error_log("Failed to log login: " . $e->getMessage());
    }
}

// 获取系统配置
function getSystemConfig($pdo, $key) {
    try {
        $stmt = $pdo->prepare("SELECT config_value FROM system_config WHERE config_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();
        return $result ? $result['config_value'] : null;
    } catch (Exception $e) {
        error_log("Failed to get system config: " . $e->getMessage());
        return null;
    }
}

// 检查用户是否过期
function isUserExpired($expiresAt) {
    if (!$expiresAt) {
        return true; // 没有到期时间视为过期
    }

    $currentTime = new DateTime();
    $expiryTime = new DateTime($expiresAt);

    return $currentTime >= $expiryTime;
}

// 计算剩余天数（用于显示）
function calculateRemainingDays($expiresAt) {
    if (!$expiresAt) {
        return 0;
    }

    $currentTime = new DateTime();
    $expiryTime = new DateTime($expiresAt);

    if ($currentTime >= $expiryTime) {
        return 0;
    }

    $diff = $currentTime->diff($expiryTime);
    return $diff->days;
}

// 更新用户状态（基于到期时间）
function updateUserStatus($pdo, $userId) {
    try {
        $stmt = $pdo->prepare("SELECT expires_at FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();

        if (!$user) {
            return false;
        }

        $isExpired = isUserExpired($user['expires_at']);
        $newStatus = $isExpired ? 'expired' : 'active';

        $stmt = $pdo->prepare("
            UPDATE users
            SET status = ?, updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$newStatus, $userId]);

        return !$isExpired;
    } catch (Exception $e) {
        error_log("Failed to update user status: " . $e->getMessage());
        return false;
    }
}

// 延长用户到期时间
function extendUserExpiry($pdo, $userId, $days) {
    try {
        $stmt = $pdo->prepare("SELECT expires_at FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        $user = $stmt->fetch();

        if (!$user) {
            return false;
        }

        $currentTime = new DateTime();
        $currentExpiry = $user['expires_at'] ? new DateTime($user['expires_at']) : null;

        // 如果当前到期时间晚于现在，在原基础上延长；否则从现在开始计算
        $baseTime = ($currentExpiry && $currentExpiry > $currentTime) ? $currentExpiry : $currentTime;
        $newExpiry = $baseTime->add(new DateInterval("P{$days}D"));

        $stmt = $pdo->prepare("
            UPDATE users
            SET expires_at = ?, status = 'active', updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$newExpiry->format('Y-m-d H:i:s'), $userId]);

        return $newExpiry->format('Y-m-d H:i:s');
    } catch (Exception $e) {
        error_log("Failed to extend user expiry: " . $e->getMessage());
        return false;
    }
}
?>
