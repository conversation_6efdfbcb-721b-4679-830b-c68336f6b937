{"version": 3, "file": "parsePhoneNumber.test.js", "names": ["parsePhoneNumber", "parameters", "push", "metadata", "_parsePhoneNumber", "apply", "USE_NON_GEOGRAPHIC_COUNTRY_CODE", "describe", "it", "nationalNumber", "should", "equal", "expect", "to", "be", "undefined", "phoneNumber", "number", "country", "countryCallingCode", "defaultCallingCode", "isPossible", "phoneNumber2", "testCorrectness", "expectedResult", "result", "extract", "defaultCountry", "not", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["../source/parsePhoneNumber.test.js"], "sourcesContent": ["import _parsePhoneNumber from './parsePhoneNumber.js'\r\nimport metadata from '../metadata.min.json' assert { type: 'json' }\r\n\r\nfunction parsePhoneNumber(...parameters) {\r\n\tparameters.push(metadata)\r\n\treturn _parsePhoneNumber.apply(this, parameters)\r\n}\r\n\r\nconst USE_NON_GEOGRAPHIC_COUNTRY_CODE = false\r\n\r\ndescribe('parsePhoneNumber', () => {\r\n\tit('should parse phone numbers from string', () => {\r\n\t\tparsePhoneNumber('Phone: 8 (800) 555 35 35.', 'RU').nationalNumber.should.equal('8005553535')\r\n\t\texpect(parsePhoneNumber('3', 'RU')).to.be.undefined\r\n\t})\r\n\r\n\tit('should work in edge cases', () => {\r\n\t\texpect(parsePhoneNumber('')).to.be.undefined\r\n\t})\r\n\r\n\tit('should parse phone numbers when invalid country code is passed', () => {\r\n\t\tparsePhoneNumber('Phone: +7 (800) 555 35 35.', 'XX').nationalNumber.should.equal('8005553535')\r\n\t\texpect(parsePhoneNumber('Phone: 8 (800) 555-35-35.', 'XX')).to.be.undefined\r\n\t})\r\n\r\n\r\n\tit('should parse non-geographic numbering plan phone numbers (extended)', () => {\r\n\t\tconst phoneNumber = parsePhoneNumber('+870773111632')\r\n\t\tphoneNumber.number.should.equal('+870773111632')\r\n\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\tphoneNumber.country.should.equal('001')\r\n\t\t} else {\r\n\t\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\t}\r\n\t\tphoneNumber.countryCallingCode.should.equal('870')\r\n\t})\r\n\r\n\tit('should parse non-geographic numbering plan phone numbers (default country code) (extended)', () => {\r\n\t\tconst phoneNumber = parsePhoneNumber('773111632', { defaultCallingCode: '870' })\r\n\t\tphoneNumber.number.should.equal('+870773111632')\r\n\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\tphoneNumber.country.should.equal('001')\r\n\t\t} else {\r\n\t\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\t}\r\n\t\tphoneNumber.countryCallingCode.should.equal('870')\r\n\t})\r\n\r\n\tit('should determine the possibility of non-geographic phone numbers', () => {\r\n\t\tconst phoneNumber = parsePhoneNumber('+870773111632')\r\n\t\tphoneNumber.isPossible().should.equal(true)\r\n\t\tconst phoneNumber2 = parsePhoneNumber('+8707731116321')\r\n\t\tphoneNumber2.isPossible().should.equal(false)\r\n\t})\r\n\r\n\tit('should support `extract: false` flag', () => {\r\n\t\tconst testCorrectness = (number, expectedResult) => {\r\n\t\t\tconst result = expect(parsePhoneNumber(number, { extract: false, defaultCountry: 'US' }))\r\n\t\t\tif (expectedResult) {\r\n\t\t\t\tresult.to.not.be.undefined\r\n\t\t\t} else {\r\n\t\t\t\tresult.to.be.undefined\r\n\t\t\t}\r\n\t\t}\r\n\t\ttestCorrectness('Call: (*************', false)\r\n\t\ttestCorrectness('(*************x', false)\r\n\t\ttestCorrectness('(*************', true)\r\n\t\ttestCorrectness('- (************* -', true)\r\n\t\ttestCorrectness('+****************', true)\r\n\t\ttestCorrectness(' +****************', false)\r\n\t})\r\n\r\n\tit('should not prematurely strip a possible national prefix from Chinese numbers', () => {\r\n\t\t// https://gitlab.com/catamphetamine/libphonenumber-js/-/issues/57\r\n\t\tconst phoneNumber = parsePhoneNumber('+86123456789')\r\n\t\tphoneNumber.isPossible().should.equal(true)\r\n\t\tphoneNumber.isValid().should.equal(false)\r\n\t\tphoneNumber.nationalNumber.should.equal('123456789')\r\n\t})\r\n})\r\n"], "mappings": ";;AAAA;;AACA;;;;AAEA,SAASA,gBAAT,GAAyC;EAAA,kCAAZC,UAAY;IAAZA,UAAY;EAAA;;EACxCA,UAAU,CAACC,IAAX,CAAgBC,uBAAhB;EACA,OAAOC,6BAAA,CAAkBC,KAAlB,CAAwB,IAAxB,EAA8BJ,UAA9B,CAAP;AACA;;AAED,IAAMK,+BAA+B,GAAG,KAAxC;AAEAC,QAAQ,CAAC,kBAAD,EAAqB,YAAM;EAClCC,EAAE,CAAC,wCAAD,EAA2C,YAAM;IAClDR,gBAAgB,CAAC,2BAAD,EAA8B,IAA9B,CAAhB,CAAoDS,cAApD,CAAmEC,MAAnE,CAA0EC,KAA1E,CAAgF,YAAhF;IACAC,MAAM,CAACZ,gBAAgB,CAAC,GAAD,EAAM,IAAN,CAAjB,CAAN,CAAoCa,EAApC,CAAuCC,EAAvC,CAA0CC,SAA1C;EACA,CAHC,CAAF;EAKAP,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrCI,MAAM,CAACZ,gBAAgB,CAAC,EAAD,CAAjB,CAAN,CAA6Ba,EAA7B,CAAgCC,EAAhC,CAAmCC,SAAnC;EACA,CAFC,CAAF;EAIAP,EAAE,CAAC,gEAAD,EAAmE,YAAM;IAC1ER,gBAAgB,CAAC,4BAAD,EAA+B,IAA/B,CAAhB,CAAqDS,cAArD,CAAoEC,MAApE,CAA2EC,KAA3E,CAAiF,YAAjF;IACAC,MAAM,CAACZ,gBAAgB,CAAC,2BAAD,EAA8B,IAA9B,CAAjB,CAAN,CAA4Da,EAA5D,CAA+DC,EAA/D,CAAkEC,SAAlE;EACA,CAHC,CAAF;EAMAP,EAAE,CAAC,qEAAD,EAAwE,YAAM;IAC/E,IAAMQ,WAAW,GAAGhB,gBAAgB,CAAC,eAAD,CAApC;IACAgB,WAAW,CAACC,MAAZ,CAAmBP,MAAnB,CAA0BC,KAA1B,CAAgC,eAAhC;;IACA,IAAIL,+BAAJ,EAAqC;MACpCU,WAAW,CAACE,OAAZ,CAAoBR,MAApB,CAA2BC,KAA3B,CAAiC,KAAjC;IACA,CAFD,MAEO;MACNC,MAAM,CAACI,WAAW,CAACE,OAAb,CAAN,CAA4BL,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACA;;IACDC,WAAW,CAACG,kBAAZ,CAA+BT,MAA/B,CAAsCC,KAAtC,CAA4C,KAA5C;EACA,CATC,CAAF;EAWAH,EAAE,CAAC,4FAAD,EAA+F,YAAM;IACtG,IAAMQ,WAAW,GAAGhB,gBAAgB,CAAC,WAAD,EAAc;MAAEoB,kBAAkB,EAAE;IAAtB,CAAd,CAApC;IACAJ,WAAW,CAACC,MAAZ,CAAmBP,MAAnB,CAA0BC,KAA1B,CAAgC,eAAhC;;IACA,IAAIL,+BAAJ,EAAqC;MACpCU,WAAW,CAACE,OAAZ,CAAoBR,MAApB,CAA2BC,KAA3B,CAAiC,KAAjC;IACA,CAFD,MAEO;MACNC,MAAM,CAACI,WAAW,CAACE,OAAb,CAAN,CAA4BL,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACA;;IACDC,WAAW,CAACG,kBAAZ,CAA+BT,MAA/B,CAAsCC,KAAtC,CAA4C,KAA5C;EACA,CATC,CAAF;EAWAH,EAAE,CAAC,kEAAD,EAAqE,YAAM;IAC5E,IAAMQ,WAAW,GAAGhB,gBAAgB,CAAC,eAAD,CAApC;IACAgB,WAAW,CAACK,UAAZ,GAAyBX,MAAzB,CAAgCC,KAAhC,CAAsC,IAAtC;IACA,IAAMW,YAAY,GAAGtB,gBAAgB,CAAC,gBAAD,CAArC;IACAsB,YAAY,CAACD,UAAb,GAA0BX,MAA1B,CAAiCC,KAAjC,CAAuC,KAAvC;EACA,CALC,CAAF;EAOAH,EAAE,CAAC,sCAAD,EAAyC,YAAM;IAChD,IAAMe,eAAe,GAAG,SAAlBA,eAAkB,CAACN,MAAD,EAASO,cAAT,EAA4B;MACnD,IAAMC,MAAM,GAAGb,MAAM,CAACZ,gBAAgB,CAACiB,MAAD,EAAS;QAAES,OAAO,EAAE,KAAX;QAAkBC,cAAc,EAAE;MAAlC,CAAT,CAAjB,CAArB;;MACA,IAAIH,cAAJ,EAAoB;QACnBC,MAAM,CAACZ,EAAP,CAAUe,GAAV,CAAcd,EAAd,CAAiBC,SAAjB;MACA,CAFD,MAEO;QACNU,MAAM,CAACZ,EAAP,CAAUC,EAAV,CAAaC,SAAb;MACA;IACD,CAPD;;IAQAQ,eAAe,CAAC,sBAAD,EAAyB,KAAzB,CAAf;IACAA,eAAe,CAAC,iBAAD,EAAoB,KAApB,CAAf;IACAA,eAAe,CAAC,gBAAD,EAAmB,IAAnB,CAAf;IACAA,eAAe,CAAC,oBAAD,EAAuB,IAAvB,CAAf;IACAA,eAAe,CAAC,mBAAD,EAAsB,IAAtB,CAAf;IACAA,eAAe,CAAC,oBAAD,EAAuB,KAAvB,CAAf;EACA,CAfC,CAAF;EAiBAf,EAAE,CAAC,8EAAD,EAAiF,YAAM;IACxF;IACA,IAAMQ,WAAW,GAAGhB,gBAAgB,CAAC,cAAD,CAApC;IACAgB,WAAW,CAACK,UAAZ,GAAyBX,MAAzB,CAAgCC,KAAhC,CAAsC,IAAtC;IACAK,WAAW,CAACa,OAAZ,GAAsBnB,MAAtB,CAA6BC,KAA7B,CAAmC,KAAnC;IACAK,WAAW,CAACP,cAAZ,CAA2BC,MAA3B,CAAkCC,KAAlC,CAAwC,WAAxC;EACA,CANC,CAAF;AAOA,CArEO,CAAR"}