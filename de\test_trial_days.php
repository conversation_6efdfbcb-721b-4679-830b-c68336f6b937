<?php
// 测试试用天数配置
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');

require_once 'config.php';

try {
    $pdo = getDBConnection();
    if (!$pdo) {
        echo json_encode(['success' => false, 'message' => '数据库连接失败']);
        exit();
    }
    
    // 获取当前配置
    $configValue = getSystemConfig($pdo, 'default_trial_days');
    $defaultTrialDays = ($configValue !== null) ? (int)$configValue : 7;
    
    // 模拟注册逻辑
    $expiryDate = new DateTime();
    if ($defaultTrialDays > 0) {
        $expiryDate->add(new DateInterval("P{$defaultTrialDays}D"));
        $expiryDate->setTime(23, 59, 59);
    } else {
        $expiryDate->setTime(0, 0, 0);
    }
    
    $initialStatus = ($defaultTrialDays > 0) ? 'active' : 'expired';
    
    echo json_encode([
        'success' => true,
        'message' => '试用天数配置测试',
        'data' => [
            'config_value' => $configValue,
            'default_trial_days' => $defaultTrialDays,
            'initial_status' => $initialStatus,
            'expiry_date' => $expiryDate->format('Y-m-d H:i:s'),
            'current_time' => (new DateTime())->format('Y-m-d H:i:s'),
            'logic_explanation' => [
                'config_null_check' => $configValue === null ? 'Config is null, using default 7' : 'Config exists: ' . $configValue,
                'trial_days_logic' => $defaultTrialDays > 0 ? 'Has trial period' : 'No trial period',
                'status_logic' => $initialStatus === 'active' ? 'User starts active' : 'User starts expired'
            ]
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '测试失败: ' . $e->getMessage()
    ]);
}
?>
