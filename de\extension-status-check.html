<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扩展状态检查 - Extension Status Check</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .status-card h3 {
            margin-top: 0;
            color: #fff;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .result {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            margin: 8px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            border-left: 3px solid #4CAF50;
        }
        
        .warning {
            border-left-color: #ff9800;
            background: rgba(255, 152, 0, 0.1);
        }
        
        .error {
            border-left-color: #f44336;
            background: rgba(244, 67, 54, 0.1);
        }
        
        .success {
            border-left-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background: #ff9800;
        }
        
        .btn-warning:hover {
            background: #f57c00;
        }
        
        .console-output {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .fix-instructions {
            background: rgba(255, 152, 0, 0.2);
            border: 1px solid rgba(255, 152, 0, 0.3);
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .fix-instructions h3 {
            margin-top: 0;
            color: #ff9800;
        }
        
        .step {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 扩展状态检查</h1>
        <h2>Extension Status Check & Troubleshooting</h2>
        <p>检查扩展是否正确加载和工作</p>
    </div>
    
    <div class="status-card">
        <h3>🔍 扩展加载状态</h3>
        <button class="btn" onclick="checkExtensionStatus()">检查扩展状态</button>
        <div id="extension-status"></div>
    </div>
    
    <div class="status-card">
        <h3>📍 地理位置API状态</h3>
        <button class="btn" onclick="checkGeolocationAPI()">检查地理位置</button>
        <div id="geolocation-status"></div>
    </div>
    
    <div class="status-card">
        <h3>🕐 时区API状态</h3>
        <button class="btn" onclick="checkTimezoneAPI()">检查时区</button>
        <div id="timezone-status"></div>
    </div>
    
    <div class="status-card">
        <h3>📝 控制台日志</h3>
        <button class="btn" onclick="captureConsoleLogs()">捕获控制台日志</button>
        <button class="btn btn-warning" onclick="clearConsoleLogs()">清除日志</button>
        <div class="console-output" id="console-logs">等待捕获日志...</div>
    </div>
    
    <div class="fix-instructions" id="fix-instructions" style="display: none;">
        <h3>🛠️ 修复指南</h3>
        <div id="fix-steps"></div>
    </div>
    
    <script>
        let consoleLogs = [];
        let originalConsoleLog = console.log;
        let originalConsoleWarn = console.warn;
        let originalConsoleError = console.error;
        
        // 拦截控制台输出
        function interceptConsole() {
            console.log = function(...args) {
                consoleLogs.push({type: 'log', message: args.join(' '), timestamp: new Date().toISOString()});
                return originalConsoleLog.apply(console, args);
            };
            
            console.warn = function(...args) {
                consoleLogs.push({type: 'warn', message: args.join(' '), timestamp: new Date().toISOString()});
                return originalConsoleWarn.apply(console, args);
            };
            
            console.error = function(...args) {
                consoleLogs.push({type: 'error', message: args.join(' '), timestamp: new Date().toISOString()});
                return originalConsoleError.apply(console, args);
            };
        }
        
        function addResult(containerId, content, type = 'result') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = content;
            container.appendChild(div);
        }
        
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }
        
        function checkExtensionStatus() {
            clearResults('extension-status');
            addResult('extension-status', '🔍 检查扩展状态...', 'result');
            
            // 检查扩展标记
            const extensionMarkers = [
                'globalTimezoneSpooferActive',
                'Global Timezone & Location Spoofer'
            ];
            
            let extensionDetected = false;
            
            // 检查全局变量
            if (window.globalTimezoneSpooferActive) {
                addResult('extension-status', '✅ 检测到扩展全局标记', 'success');
                extensionDetected = true;
            } else {
                addResult('extension-status', '❌ 未检测到扩展全局标记', 'error');
            }
            
            // 检查localStorage设置
            const savedSettings = localStorage.getItem('globalTimezoneSpoofer_selectedCity');
            if (savedSettings) {
                try {
                    const cityData = JSON.parse(savedSettings);
                    addResult('extension-status', `✅ 找到扩展设置: ${cityData.city}, ${cityData.country}`, 'success');
                    extensionDetected = true;
                } catch (e) {
                    addResult('extension-status', '⚠️ 扩展设置格式错误', 'warning');
                }
            } else {
                addResult('extension-status', '⚠️ 未找到扩展设置', 'warning');
            }
            
            // 检查Date API是否被修改
            const originalDate = Date;
            const testDate = new Date();
            
            if (testDate.getTimezoneOffset.toString().includes('dynamicOffset')) {
                addResult('extension-status', '✅ Date API已被扩展修改', 'success');
                extensionDetected = true;
            } else {
                addResult('extension-status', '❌ Date API未被修改', 'error');
            }
            
            // 检查navigator.geolocation是否被修改
            if (navigator.geolocation && navigator.geolocation.getCurrentPosition.toString().includes('LOCATION_DATA')) {
                addResult('extension-status', '✅ 地理位置API已被扩展修改', 'success');
                extensionDetected = true;
            } else {
                addResult('extension-status', '❌ 地理位置API未被修改', 'error');
            }
            
            // 检查控制台日志
            const extensionLogs = consoleLogs.filter(log => 
                log.message.includes('Global Timezone') || 
                log.message.includes('🌍') ||
                log.message.includes('Spoofer')
            );
            
            if (extensionLogs.length > 0) {
                addResult('extension-status', `✅ 检测到 ${extensionLogs.length} 条扩展日志`, 'success');
                extensionDetected = true;
            } else {
                addResult('extension-status', '❌ 未检测到扩展日志', 'error');
            }
            
            // 总结状态
            if (extensionDetected) {
                addResult('extension-status', '✅ 扩展已加载但可能部分功能失效', 'warning');
            } else {
                addResult('extension-status', '❌ 扩展未正确加载', 'error');
                showFixInstructions('extension_not_loaded');
            }
        }
        
        function checkGeolocationAPI() {
            clearResults('geolocation-status');
            addResult('geolocation-status', '📍 检查地理位置API...', 'result');
            
            if (!navigator.geolocation) {
                addResult('geolocation-status', '❌ 地理位置API不可用', 'error');
                return;
            }
            
            // 检查API是否被修改
            const geoString = navigator.geolocation.getCurrentPosition.toString();
            if (geoString.includes('LOCATION_DATA') || geoString.includes('FIXED_LAT')) {
                addResult('geolocation-status', '✅ 地理位置API已被扩展修改', 'success');
            } else {
                addResult('geolocation-status', '❌ 地理位置API未被修改', 'error');
            }
            
            // 测试地理位置
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;
                    
                    addResult('geolocation-status', `📍 当前坐标: ${lat.toFixed(4)}, ${lng.toFixed(4)}`, 'result');
                    
                    // 检查是否在美国范围内
                    if (lat >= 24.396308 && lat <= 49.384358 && lng >= -125.0 && lng <= -66.93457) {
                        addResult('geolocation-status', '✅ 坐标在美国范围内 - 扩展工作正常', 'success');
                    } else {
                        addResult('geolocation-status', '❌ 坐标不在美国范围内 - 扩展未生效', 'error');
                        showFixInstructions('geolocation_not_working');
                    }
                    
                    addResult('geolocation-status', `📏 精度: ${position.coords.accuracy}米`, 'result');
                },
                (error) => {
                    addResult('geolocation-status', `❌ 地理位置错误: ${error.message}`, 'error');
                }
            );
        }
        
        function checkTimezoneAPI() {
            clearResults('timezone-status');
            addResult('timezone-status', '🕐 检查时区API...', 'result');
            
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            const offset = new Date().getTimezoneOffset();
            
            addResult('timezone-status', `🕐 当前时区: ${timezone}`, 'result');
            addResult('timezone-status', `⏰ 时区偏移: ${offset} 分钟`, 'result');
            
            // 检查是否是美国时区
            const usTimezones = [
                'America/New_York', 'America/Chicago', 'America/Denver', 
                'America/Los_Angeles', 'America/Phoenix', 'America/Anchorage',
                'Pacific/Honolulu'
            ];
            
            if (usTimezones.includes(timezone)) {
                addResult('timezone-status', '✅ 时区设置正确 - 扩展工作正常', 'success');
            } else {
                addResult('timezone-status', '❌ 时区不是美国时区 - 扩展未生效', 'error');
                showFixInstructions('timezone_not_working');
            }
            
            // 检查Date API
            const dateString = new Date().toString();
            addResult('timezone-status', `📅 Date.toString(): ${dateString}`, 'result');
            
            // 检查getTimezoneOffset是否被修改
            const offsetString = Date.prototype.getTimezoneOffset.toString();
            if (offsetString.includes('dynamicOffset')) {
                addResult('timezone-status', '✅ getTimezoneOffset已被扩展修改', 'success');
            } else {
                addResult('timezone-status', '❌ getTimezoneOffset未被修改', 'error');
            }
        }
        
        function captureConsoleLogs() {
            const container = document.getElementById('console-logs');
            
            if (consoleLogs.length === 0) {
                container.innerHTML = '📝 暂无控制台日志';
                return;
            }
            
            let logHtml = '';
            consoleLogs.forEach(log => {
                const time = new Date(log.timestamp).toLocaleTimeString();
                const typeIcon = log.type === 'error' ? '❌' : log.type === 'warn' ? '⚠️' : '📝';
                logHtml += `<div style="margin: 5px 0; padding: 5px; background: rgba(255,255,255,0.05); border-radius: 3px;">
                    ${typeIcon} [${time}] ${log.message}
                </div>`;
            });
            
            container.innerHTML = logHtml;
        }
        
        function clearConsoleLogs() {
            consoleLogs = [];
            document.getElementById('console-logs').innerHTML = '📝 日志已清除';
        }
        
        function showFixInstructions(problemType) {
            const fixContainer = document.getElementById('fix-instructions');
            const stepsContainer = document.getElementById('fix-steps');
            
            let steps = '';
            
            switch (problemType) {
                case 'extension_not_loaded':
                    steps = `
                        <div class="step">
                            <strong>步骤 1:</strong> 检查扩展是否已安装
                            <br>• 打开 Chrome 扩展管理页面 (chrome://extensions/)
                            <br>• 确认 "Global Timezone & Location Spoofer" 已安装并启用
                        </div>
                        <div class="step">
                            <strong>步骤 2:</strong> 重新加载扩展
                            <br>• 在扩展管理页面点击扩展的"重新加载"按钮
                            <br>• 刷新当前页面
                        </div>
                        <div class="step">
                            <strong>步骤 3:</strong> 检查权限
                            <br>• 确认扩展有访问所有网站的权限
                            <br>• 检查是否被其他安全软件阻止
                        </div>
                    `;
                    break;
                    
                case 'geolocation_not_working':
                    steps = `
                        <div class="step">
                            <strong>步骤 1:</strong> 检查扩展设置
                            <br>• 点击扩展图标打开设置面板
                            <br>• 确认选择了美国地区
                            <br>• 点击"应用设置"
                        </div>
                        <div class="step">
                            <strong>步骤 2:</strong> 清除浏览器缓存
                            <br>• 清除浏览器缓存和Cookie
                            <br>• 重启浏览器
                        </div>
                        <div class="step">
                            <strong>步骤 3:</strong> 检查地理位置权限
                            <br>• 确认网站有地理位置权限
                            <br>• 在地址栏左侧点击锁图标检查权限
                        </div>
                    `;
                    break;
                    
                case 'timezone_not_working':
                    steps = `
                        <div class="step">
                            <strong>步骤 1:</strong> 重新选择地区
                            <br>• 打开扩展设置面板
                            <br>• 重新选择美国地区
                            <br>• 确认设置已保存
                        </div>
                        <div class="step">
                            <strong>步骤 2:</strong> 刷新页面
                            <br>• 完全刷新页面 (Ctrl+F5)
                            <br>• 或关闭标签页重新打开
                        </div>
                        <div class="step">
                            <strong>步骤 3:</strong> 检查冲突
                            <br>• 暂时禁用其他扩展
                            <br>• 检查是否有时区相关的其他扩展
                        </div>
                    `;
                    break;
            }
            
            stepsContainer.innerHTML = steps;
            fixContainer.style.display = 'block';
        }
        
        // 页面加载时开始拦截控制台
        window.addEventListener('load', () => {
            interceptConsole();
            
            // 自动运行基础检查
            setTimeout(() => {
                checkExtensionStatus();
            }, 1000);
        });
        
        // 添加一键修复按钮
        function quickFix() {
            // 尝试重新加载扩展设置
            const savedSettings = localStorage.getItem('globalTimezoneSpoofer_selectedCity');
            if (savedSettings) {
                try {
                    const cityData = JSON.parse(savedSettings);
                    // 重新设置
                    localStorage.setItem('globalTimezoneSpoofer_selectedCity', JSON.stringify(cityData));
                    addResult('extension-status', '🔧 尝试重新应用扩展设置', 'warning');
                } catch (e) {
                    addResult('extension-status', '❌ 设置格式错误，需要重新配置', 'error');
                }
            }
            
            // 刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        }
        
        // 在header中添加一键修复按钮
        const header = document.querySelector('.header');
        const quickFixBtn = document.createElement('button');
        quickFixBtn.className = 'btn btn-warning';
        quickFixBtn.textContent = '🔧 一键修复';
        quickFixBtn.onclick = quickFix;
        header.appendChild(quickFixBtn);
    </script>
</body>
</html>
