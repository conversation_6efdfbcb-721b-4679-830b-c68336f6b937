{"version": 3, "file": "checkNumberLength.js", "names": ["mergeArrays", "checkNumberLength", "nationalNumber", "metadata", "checkNumberLengthForType", "undefined", "type", "type_info", "possible_lengths", "possibleLengths", "mobile_type", "actual_length", "length", "minimum_length", "indexOf"], "sources": ["../../source/helpers/checkNumberLength.js"], "sourcesContent": ["import mergeArrays from './mergeArrays.js'\r\n\r\nexport default function checkNumberLength(nationalNumber, metadata) {\r\n\treturn checkNumberLengthForType(nationalNumber, undefined, metadata)\r\n}\r\n\r\n// Checks whether a number is possible for the country based on its length.\r\n// Should only be called for the \"new\" metadata which has \"possible lengths\".\r\nexport function checkNumberLengthForType(nationalNumber, type, metadata) {\r\n\tconst type_info = metadata.type(type)\r\n\r\n\t// There should always be \"<possiblePengths/>\" set for every type element.\r\n\t// This is declared in the XML schema.\r\n\t// For size efficiency, where a sub-description (e.g. fixed-line)\r\n\t// has the same \"<possiblePengths/>\" as the \"general description\", this is missing,\r\n\t// so we fall back to the \"general description\". Where no numbers of the type\r\n\t// exist at all, there is one possible length (-1) which is guaranteed\r\n\t// not to match the length of any real phone number.\r\n\tlet possible_lengths = type_info && type_info.possibleLengths() || metadata.possibleLengths()\r\n\t// let local_lengths    = type_info && type.possibleLengthsLocal() || metadata.possibleLengthsLocal()\r\n\r\n\t// Metadata before version `1.0.18` didn't contain `possible_lengths`.\r\n\tif (!possible_lengths) {\r\n\t\treturn 'IS_POSSIBLE'\r\n\t}\r\n\r\n\tif (type === 'FIXED_LINE_OR_MOBILE') {\r\n\t\t// No such country in metadata.\r\n\t\t/* istanbul ignore next */\r\n\t\tif (!metadata.type('FIXED_LINE')) {\r\n\t\t\t// The rare case has been encountered where no fixedLine data is available\r\n\t\t\t// (true for some non-geographic entities), so we just check mobile.\r\n\t\t\treturn checkNumberLengthForType(nationalNumber, 'MOBILE', metadata)\r\n\t\t}\r\n\r\n\t\tconst mobile_type = metadata.type('MOBILE')\r\n\t\tif (mobile_type) {\r\n\t\t\t// Merge the mobile data in if there was any. \"Concat\" creates a new\r\n\t\t\t// array, it doesn't edit possible_lengths in place, so we don't need a copy.\r\n\t\t\t// Note that when adding the possible lengths from mobile, we have\r\n\t\t\t// to again check they aren't empty since if they are this indicates\r\n\t\t\t// they are the same as the general desc and should be obtained from there.\r\n\t\t\tpossible_lengths = mergeArrays(possible_lengths, mobile_type.possibleLengths())\r\n\t\t\t// The current list is sorted; we need to merge in the new list and\r\n\t\t\t// re-sort (duplicates are okay). Sorting isn't so expensive because\r\n\t\t\t// the lists are very small.\r\n\r\n\t\t\t// if (local_lengths) {\r\n\t\t\t// \tlocal_lengths = mergeArrays(local_lengths, mobile_type.possibleLengthsLocal())\r\n\t\t\t// } else {\r\n\t\t\t// \tlocal_lengths = mobile_type.possibleLengthsLocal()\r\n\t\t\t// }\r\n\t\t}\r\n\t}\r\n\t// If the type doesn't exist then return 'INVALID_LENGTH'.\r\n\telse if (type && !type_info) {\r\n\t\treturn 'INVALID_LENGTH'\r\n\t}\r\n\r\n\tconst actual_length = nationalNumber.length\r\n\r\n\t// In `libphonenumber-js` all \"local-only\" formats are dropped for simplicity.\r\n\t// // This is safe because there is never an overlap beween the possible lengths\r\n\t// // and the local-only lengths; this is checked at build time.\r\n\t// if (local_lengths && local_lengths.indexOf(nationalNumber.length) >= 0)\r\n\t// {\r\n\t// \treturn 'IS_POSSIBLE_LOCAL_ONLY'\r\n\t// }\r\n\r\n\tconst minimum_length = possible_lengths[0]\r\n\r\n\tif (minimum_length === actual_length) {\r\n\t\treturn 'IS_POSSIBLE'\r\n\t}\r\n\r\n\tif (minimum_length > actual_length) {\r\n\t\treturn 'TOO_SHORT'\r\n\t}\r\n\r\n\tif (possible_lengths[possible_lengths.length - 1] < actual_length) {\r\n\t\treturn 'TOO_LONG'\r\n\t}\r\n\r\n\t// We skip the first element since we've already checked it.\r\n\treturn possible_lengths.indexOf(actual_length, 1) >= 0 ? 'IS_POSSIBLE' : 'INVALID_LENGTH'\r\n}"], "mappings": "AAAA,OAAOA,WAAP,MAAwB,kBAAxB;AAEA,eAAe,SAASC,iBAAT,CAA2BC,cAA3B,EAA2CC,QAA3C,EAAqD;EACnE,OAAOC,wBAAwB,CAACF,cAAD,EAAiBG,SAAjB,EAA4BF,QAA5B,CAA/B;AACA,C,CAED;AACA;;AACA,OAAO,SAASC,wBAAT,CAAkCF,cAAlC,EAAkDI,IAAlD,EAAwDH,QAAxD,EAAkE;EACxE,IAAMI,SAAS,GAAGJ,QAAQ,CAACG,IAAT,CAAcA,IAAd,CAAlB,CADwE,CAGxE;EACA;EACA;EACA;EACA;EACA;EACA;;EACA,IAAIE,gBAAgB,GAAGD,SAAS,IAAIA,SAAS,CAACE,eAAV,EAAb,IAA4CN,QAAQ,CAACM,eAAT,EAAnE,CAVwE,CAWxE;EAEA;;EACA,IAAI,CAACD,gBAAL,EAAuB;IACtB,OAAO,aAAP;EACA;;EAED,IAAIF,IAAI,KAAK,sBAAb,EAAqC;IACpC;;IACA;IACA,IAAI,CAACH,QAAQ,CAACG,IAAT,CAAc,YAAd,CAAL,EAAkC;MACjC;MACA;MACA,OAAOF,wBAAwB,CAACF,cAAD,EAAiB,QAAjB,EAA2BC,QAA3B,CAA/B;IACA;;IAED,IAAMO,WAAW,GAAGP,QAAQ,CAACG,IAAT,CAAc,QAAd,CAApB;;IACA,IAAII,WAAJ,EAAiB;MAChB;MACA;MACA;MACA;MACA;MACAF,gBAAgB,GAAGR,WAAW,CAACQ,gBAAD,EAAmBE,WAAW,CAACD,eAAZ,EAAnB,CAA9B,CANgB,CAOhB;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;IACA;EACD,CA3BD,CA4BA;EA5BA,KA6BK,IAAIH,IAAI,IAAI,CAACC,SAAb,EAAwB;IAC5B,OAAO,gBAAP;EACA;;EAED,IAAMI,aAAa,GAAGT,cAAc,CAACU,MAArC,CAnDwE,CAqDxE;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,IAAMC,cAAc,GAAGL,gBAAgB,CAAC,CAAD,CAAvC;;EAEA,IAAIK,cAAc,KAAKF,aAAvB,EAAsC;IACrC,OAAO,aAAP;EACA;;EAED,IAAIE,cAAc,GAAGF,aAArB,EAAoC;IACnC,OAAO,WAAP;EACA;;EAED,IAAIH,gBAAgB,CAACA,gBAAgB,CAACI,MAAjB,GAA0B,CAA3B,CAAhB,GAAgDD,aAApD,EAAmE;IAClE,OAAO,UAAP;EACA,CAzEuE,CA2ExE;;;EACA,OAAOH,gBAAgB,CAACM,OAAjB,CAAyBH,aAAzB,EAAwC,CAAxC,KAA8C,CAA9C,GAAkD,aAAlD,GAAkE,gBAAzE;AACA"}