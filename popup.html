<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Global Timezone & Location Spoofer</title>
    <style>
        body {
            width: 350px;
            min-height: 400px;
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .header {
            padding: 20px;
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .header .version {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 5px;
        }

        .content {
            padding: 20px;
        }

        .section {
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .section-title .icon {
            margin-right: 8px;
            font-size: 16px;
        }

        .region-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .region-card {
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
        }

        .region-card:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-2px);
        }

        .region-card.selected {
            background: rgba(255, 255, 255, 0.3);
            border-color: #4CAF50;
            box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
        }

        .region-card .flag {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .region-card .name {
            font-size: 13px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .region-card .timezone {
            font-size: 11px;
            opacity: 0.8;
        }

        .status {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 12px;
            font-size: 12px;
            line-height: 1.4;
        }

        .status.active {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
        }

        .current-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .current-location {
            font-weight: 600;
        }

        .current-time {
            font-size: 11px;
            opacity: 0.9;
        }

        .controls {
            margin-top: 20px;
        }

        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 13px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 8px;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #45a049;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .footer {
            text-align: center;
            padding: 15px;
            font-size: 11px;
            opacity: 0.7;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* 数据清理样式 */
        .clean-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            font-size: 12px;
        }

        .clean-description {
            font-weight: 600;
            margin-bottom: 6px;
        }

        .clean-sites {
            opacity: 0.8;
            font-size: 11px;
        }

        .clean-status {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 8px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
            text-align: center;
        }

        .clean-status.success {
            background: rgba(76, 175, 80, 0.2);
            border-color: rgba(76, 175, 80, 0.3);
        }

        .clean-status.error {
            background: rgba(244, 67, 54, 0.2);
            border-color: rgba(244, 67, 54, 0.3);
        }

        .clean-progress {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .clean-progress::before {
            content: '';
            width: 12px;
            height: 12px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        /* 清理按钮专用样式 */
        #clean-augment-btn {
            background: #FFC107 !important;
            color: #000 !important;
            font-weight: 600;
        }

        #clean-augment-btn:hover {
            background: #FFB300 !important;
            transform: translateY(-1px);
        }

        #clean-augment-btn:disabled {
            background: #FFE082 !important;
            color: #666 !important;
            cursor: not-allowed;
            transform: none;
        }

        /* Aug登录按钮样式 */
        #aug-login-btn {
            background: #2196F3 !important;
            color: white !important;
            font-weight: 600;
            margin-top: 8px;
        }

        #aug-login-btn:hover {
            background: #1976D2 !important;
            transform: translateY(-1px);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .user-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            font-size: 12px;
        }

        .btn-logout {
            background: rgba(244, 67, 54, 0.8);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .btn-logout:hover {
            background: rgba(244, 67, 54, 1);
            transform: scale(1.05);
        }

        /* 使用教程按钮样式 */
        .tutorial-section {
            text-align: center;
            margin: 15px 0 10px 0;
            padding-top: 10px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .tutorial-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 20px;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
            width: 100%;
            max-width: 200px;
        }

        .tutorial-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .tutorial-btn:active {
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌍 Global Timezone Spoofer</h1>
        <div class="version">v2.9.7 - User Region Selection</div>
        <div class="user-status" id="user-status">
            <span id="user-welcome">👤 加载中...</span>
            <button class="btn-logout" id="logout-btn" title="退出登录">🚪</button>
        </div>
    </div>

    <div class="content">
        <!-- Augment数据清理按钮 - 置顶 -->
        <div class="section">
            <button class="btn btn-secondary" id="clean-augment-btn">🧹 清理Augment数据</button>
            <div class="clean-status" id="clean-status" style="display: none;">
                <div class="clean-progress">正在清理数据...</div>
            </div>
            <button class="btn btn-secondary" id="aug-login-btn">🔑 Aug登录</button>
        </div>

        <!-- 应用设置按钮 -->
        <div class="controls">
            <button class="btn btn-primary" id="apply-btn">应用设置 / Apply Settings</button>
            <button class="btn btn-secondary" id="random-btn">随机选择 / Random</button>
        </div>

        <div class="section">
            <div class="section-title">
                <span class="icon">🎯</span>
                选择地区 / Select Region
            </div>
            <div class="region-grid">
                <div class="region-card" data-region="US">
                    <div class="flag">🇺🇸</div>
                    <div class="name">美国</div>
                    <div class="timezone">America/New_York</div>
                </div>
                <div class="region-card" data-region="TW">
                    <div class="flag">🇹🇼</div>
                    <div class="name">台湾</div>
                    <div class="timezone">Asia/Taipei</div>
                </div>
                <div class="region-card" data-region="JP">
                    <div class="flag">🇯🇵</div>
                    <div class="name">日本</div>
                    <div class="timezone">Asia/Tokyo</div>
                </div>
                <div class="region-card" data-region="SG">
                    <div class="flag">🇸🇬</div>
                    <div class="name">新加坡</div>
                    <div class="timezone">Asia/Singapore</div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">
                <span class="icon">📍</span>
                当前状态 / Current Status
            </div>
            <div class="status" id="status">
                <div class="current-info">
                    <div class="current-location" id="current-location">加载中...</div>
                    <div class="current-time" id="current-time">--:--</div>
                </div>
                <div id="current-details">正在获取当前设置...</div>
            </div>
        </div>



        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div>正在应用设置...</div>
        </div>

        <!-- 使用教程按钮 -->
        <div class="tutorial-section">
            <button id="tutorialBtn" class="tutorial-btn">
                📖 使用教程
            </button>
        </div>
    </div>

    <div class="footer">
        作者：小鱼游水 | 网址：https://xoxome.online
    </div>

    <script src="popup.js"></script>
</body>
</html>
