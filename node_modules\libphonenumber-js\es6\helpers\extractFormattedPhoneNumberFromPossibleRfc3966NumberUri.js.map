{"version": 3, "file": "extractFormattedPhoneNumberFromPossibleRfc3966NumberUri.js", "names": ["extractPhoneContext", "isPhoneContextValid", "PLUS_SIGN", "RFC3966_PREFIX_", "RFC3966_PHONE_CONTEXT_", "RFC3966_ISDN_SUBADDRESS_", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "extractFormattedPhoneNumberFromPossibleRfc3966NumberUri", "numberToParse", "extractFormattedPhoneNumber", "phoneContext", "phoneNumberString", "char<PERSON>t", "indexOfRfc3966Prefix", "indexOf", "indexOfNationalNumber", "length", "indexOfPhoneContext", "substring", "indexOfIsdn"], "sources": ["../../source/helpers/extractFormattedPhoneNumberFromPossibleRfc3966NumberUri.js"], "sourcesContent": ["import extractPhoneContext, {\r\n\tisPhoneContextValid,\r\n\tPLUS_SIGN,\r\n\tRFC3966_PREFIX_,\r\n\tRFC3966_PHONE_CONTEXT_,\r\n\tRFC3966_ISDN_SUBADDRESS_\r\n} from './extractPhoneContext.js'\r\n\r\nimport ParseError from '../ParseError.js'\r\n\r\n/**\r\n * @param  {string} numberToParse\r\n * @param  {string} nationalNumber\r\n * @return {}\r\n */\r\nexport default function extractFormattedPhoneNumberFromPossibleRfc3966NumberUri(numberToParse, {\r\n\textractFormattedPhoneNumber\r\n}) {\r\n\tconst phoneContext = extractPhoneContext(numberToParse)\r\n\tif (!isPhoneContextValid(phoneContext)) {\r\n\t\tthrow new ParseError('NOT_A_NUMBER')\r\n\t}\r\n\r\n\tlet phoneNumberString\r\n\r\n\tif (phoneContext === null) {\r\n\t\t// Extract a possible number from the string passed in.\r\n\t\t// (this strips leading characters that could not be the start of a phone number)\r\n\t\tphoneNumberString = extractFormattedPhoneNumber(numberToParse) || ''\r\n\t} else {\r\n\t\tphoneNumberString = ''\r\n\r\n\t\t// If the phone context contains a phone number prefix, we need to capture\r\n\t\t// it, whereas domains will be ignored.\r\n\t\tif (phoneContext.charAt(0) === PLUS_SIGN) {\r\n\t\t\tphoneNumberString += phoneContext\r\n\t\t}\r\n\r\n\t\t// Now append everything between the \"tel:\" prefix and the phone-context.\r\n\t\t// This should include the national number, an optional extension or\r\n\t\t// isdn-subaddress component. Note we also handle the case when \"tel:\" is\r\n\t\t// missing, as we have seen in some of the phone number inputs.\r\n\t\t// In that case, we append everything from the beginning.\r\n\t\tconst indexOfRfc3966Prefix = numberToParse.indexOf(RFC3966_PREFIX_)\r\n\t\tlet indexOfNationalNumber\r\n\t\t// RFC 3966 \"tel:\" prefix is preset at this stage because\r\n\t\t// `isPhoneContextValid()` requires it to be present.\r\n\t\t/* istanbul ignore else */\r\n\t\tif (indexOfRfc3966Prefix >= 0) {\r\n\t\t\tindexOfNationalNumber = indexOfRfc3966Prefix + RFC3966_PREFIX_.length\r\n\t\t} else {\r\n\t\t\tindexOfNationalNumber = 0\r\n\t\t}\r\n\t\tconst indexOfPhoneContext = numberToParse.indexOf(RFC3966_PHONE_CONTEXT_)\r\n\t\tphoneNumberString += numberToParse.substring(indexOfNationalNumber, indexOfPhoneContext)\r\n\t}\r\n\r\n\t// Delete the isdn-subaddress and everything after it if it is present.\r\n\t// Note extension won't appear at the same time with isdn-subaddress\r\n\t// according to paragraph 5.3 of the RFC3966 spec.\r\n\tconst indexOfIsdn = phoneNumberString.indexOf(RFC3966_ISDN_SUBADDRESS_)\r\n\tif (indexOfIsdn > 0) {\r\n\t\tphoneNumberString = phoneNumberString.substring(0, indexOfIsdn)\r\n\t}\r\n\t// If both phone context and isdn-subaddress are absent but other\r\n\t// parameters are present, the parameters are left in nationalNumber.\r\n\t// This is because we are concerned about deleting content from a potential\r\n\t// number string when there is no strong evidence that the number is\r\n\t// actually written in RFC3966.\r\n\r\n\tif (phoneNumberString !== '') {\r\n\t\treturn phoneNumberString\r\n\t}\r\n}"], "mappings": "AAAA,OAAOA,mBAAP,IACCC,mBADD,EAECC,SAFD,EAGCC,eAHD,EAICC,sBAJD,EAKCC,wBALD,QAMO,0BANP;AAQA,OAAOC,UAAP,MAAuB,kBAAvB;AAEA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,uDAAT,CAAiEC,aAAjE,QAEZ;EAAA,IADFC,2BACE,QADFA,2BACE;EACF,IAAMC,YAAY,GAAGV,mBAAmB,CAACQ,aAAD,CAAxC;;EACA,IAAI,CAACP,mBAAmB,CAACS,YAAD,CAAxB,EAAwC;IACvC,MAAM,IAAIJ,UAAJ,CAAe,cAAf,CAAN;EACA;;EAED,IAAIK,iBAAJ;;EAEA,IAAID,YAAY,KAAK,IAArB,EAA2B;IAC1B;IACA;IACAC,iBAAiB,GAAGF,2BAA2B,CAACD,aAAD,CAA3B,IAA8C,EAAlE;EACA,CAJD,MAIO;IACNG,iBAAiB,GAAG,EAApB,CADM,CAGN;IACA;;IACA,IAAID,YAAY,CAACE,MAAb,CAAoB,CAApB,MAA2BV,SAA/B,EAA0C;MACzCS,iBAAiB,IAAID,YAArB;IACA,CAPK,CASN;IACA;IACA;IACA;IACA;;;IACA,IAAMG,oBAAoB,GAAGL,aAAa,CAACM,OAAd,CAAsBX,eAAtB,CAA7B;IACA,IAAIY,qBAAJ,CAfM,CAgBN;IACA;;IACA;;IACA,IAAIF,oBAAoB,IAAI,CAA5B,EAA+B;MAC9BE,qBAAqB,GAAGF,oBAAoB,GAAGV,eAAe,CAACa,MAA/D;IACA,CAFD,MAEO;MACND,qBAAqB,GAAG,CAAxB;IACA;;IACD,IAAME,mBAAmB,GAAGT,aAAa,CAACM,OAAd,CAAsBV,sBAAtB,CAA5B;IACAO,iBAAiB,IAAIH,aAAa,CAACU,SAAd,CAAwBH,qBAAxB,EAA+CE,mBAA/C,CAArB;EACA,CAtCC,CAwCF;EACA;EACA;;;EACA,IAAME,WAAW,GAAGR,iBAAiB,CAACG,OAAlB,CAA0BT,wBAA1B,CAApB;;EACA,IAAIc,WAAW,GAAG,CAAlB,EAAqB;IACpBR,iBAAiB,GAAGA,iBAAiB,CAACO,SAAlB,CAA4B,CAA5B,EAA+BC,WAA/B,CAApB;EACA,CA9CC,CA+CF;EACA;EACA;EACA;EACA;;;EAEA,IAAIR,iBAAiB,KAAK,EAA1B,EAA8B;IAC7B,OAAOA,iBAAP;EACA;AACD"}