<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration Test - Anti-Fraud Detection</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .test-form {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 14px;
        }
        
        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .detection-results {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .result-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            margin: 8px 0;
            border-left: 3px solid #4CAF50;
        }
        
        .result-item.warning {
            border-left-color: #ff9800;
            background: rgba(255, 152, 0, 0.1);
        }
        
        .result-item.error {
            border-left-color: #f44336;
            background: rgba(244, 67, 54, 0.1);
        }
        
        .fingerprint-data {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .risk-score {
            text-align: center;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            font-size: 18px;
            font-weight: bold;
        }
        
        .risk-low {
            background: rgba(76, 175, 80, 0.2);
            border: 2px solid #4CAF50;
            color: #4CAF50;
        }
        
        .risk-medium {
            background: rgba(255, 152, 0, 0.2);
            border: 2px solid #ff9800;
            color: #ff9800;
        }
        
        .risk-high {
            background: rgba(244, 67, 54, 0.2);
            border: 2px solid #f44336;
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Registration Anti-Fraud Test</h1>
        <p>模拟注册流程，检测反欺诈系统可能的拒绝原因</p>
    </div>
    
    <div class="test-form">
        <h3>📝 模拟注册表单</h3>
        <form id="registration-form">
            <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" placeholder="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="firstName">First Name</label>
                <input type="text" id="firstName" name="firstName" placeholder="John" required>
            </div>
            
            <div class="form-group">
                <label for="lastName">Last Name</label>
                <input type="text" id="lastName" name="lastName" placeholder="Doe" required>
            </div>
            
            <div class="form-group">
                <label for="phone">Phone Number</label>
                <input type="tel" id="phone" name="phone" placeholder="+****************">
            </div>
            
            <div class="form-group">
                <label for="country">Country</label>
                <select id="country" name="country" required>
                    <option value="US">United States</option>
                    <option value="CA">Canada</option>
                    <option value="GB">United Kingdom</option>
                    <option value="AU">Australia</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="state">State/Province</label>
                <input type="text" id="state" name="state" placeholder="California">
            </div>
            
            <div class="form-group">
                <label for="city">City</label>
                <input type="text" id="city" name="city" placeholder="Los Angeles">
            </div>
            
            <button type="submit" class="btn">🚀 Test Registration</button>
        </form>
    </div>
    
    <div class="detection-results" id="results" style="display: none;">
        <h3>🔍 Anti-Fraud Detection Results</h3>
        <div id="risk-score-container"></div>
        <div id="detection-details"></div>
        <div id="fingerprint-details"></div>
    </div>
    
    <script>
        let detectionData = {};
        let riskFactors = [];
        
        document.getElementById('registration-form').addEventListener('submit', function(e) {
            e.preventDefault();
            runAntifraudDetection();
        });
        
        function runAntifraudDetection() {
            document.getElementById('results').style.display = 'block';
            clearResults();
            
            console.log('🔍 Starting anti-fraud detection simulation...');
            
            // 收集设备指纹
            collectDeviceFingerprint();
            
            // 检查地理位置一致性
            checkGeolocationConsistency();
            
            // 检查时区一致性
            checkTimezoneConsistency();
            
            // 检查网络指纹
            checkNetworkFingerprint();
            
            // 检查行为模式
            checkBehaviorPatterns();
            
            // 计算风险评分
            setTimeout(() => {
                calculateRiskScore();
            }, 2000);
        }
        
        function clearResults() {
            document.getElementById('detection-details').innerHTML = '';
            document.getElementById('fingerprint-details').innerHTML = '';
            document.getElementById('risk-score-container').innerHTML = '';
            riskFactors = [];
            detectionData = {};
        }
        
        function addResult(content, type = 'success') {
            const container = document.getElementById('detection-details');
            const div = document.createElement('div');
            div.className = `result-item ${type}`;
            div.innerHTML = content;
            container.appendChild(div);
        }
        
        function collectDeviceFingerprint() {
            addResult('📱 收集设备指纹...', 'info');
            
            detectionData.userAgent = navigator.userAgent;
            detectionData.language = navigator.language;
            detectionData.languages = navigator.languages;
            detectionData.platform = navigator.platform;
            detectionData.cookieEnabled = navigator.cookieEnabled;
            detectionData.doNotTrack = navigator.doNotTrack;
            detectionData.hardwareConcurrency = navigator.hardwareConcurrency;
            detectionData.deviceMemory = navigator.deviceMemory;
            detectionData.screenResolution = `${screen.width}x${screen.height}`;
            detectionData.colorDepth = screen.colorDepth;
            detectionData.pixelDepth = screen.pixelDepth;
            detectionData.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            detectionData.timezoneOffset = new Date().getTimezoneOffset();
            
            // Canvas指纹
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('Device fingerprint test 🔍', 2, 2);
            detectionData.canvasFingerprint = canvas.toDataURL();
            
            // WebGL指纹
            const gl = canvas.getContext('webgl');
            if (gl) {
                detectionData.webglVendor = gl.getParameter(gl.VENDOR);
                detectionData.webglRenderer = gl.getParameter(gl.RENDERER);
            }
            
            addResult('✅ 设备指纹收集完成', 'success');
            
            // 显示指纹数据
            const fingerprintContainer = document.getElementById('fingerprint-details');
            fingerprintContainer.innerHTML = `
                <h4>🔍 设备指纹数据</h4>
                <div class="fingerprint-data">${JSON.stringify(detectionData, null, 2)}</div>
            `;
        }
        
        function checkGeolocationConsistency() {
            addResult('📍 检查地理位置一致性...', 'info');
            
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;
                        
                        detectionData.geolocation = {
                            latitude: lat,
                            longitude: lng,
                            accuracy: position.coords.accuracy
                        };
                        
                        // 检查坐标是否在美国
                        if (lat >= 24.396308 && lat <= 49.384358 && lng >= -125.0 && lng <= -66.93457) {
                            addResult(`✅ GPS位置在美国: ${lat.toFixed(4)}, ${lng.toFixed(4)}`, 'success');
                        } else {
                            addResult(`❌ GPS位置不在美国: ${lat.toFixed(4)}, ${lng.toFixed(4)}`, 'error');
                            riskFactors.push('GPS位置与声明国家不符');
                        }
                        
                        // 检查精度
                        if (position.coords.accuracy > 1000) {
                            addResult(`⚠️ GPS精度较低: ${position.coords.accuracy}米`, 'warning');
                            riskFactors.push('GPS精度异常');
                        }
                    },
                    (error) => {
                        addResult(`❌ 无法获取GPS位置: ${error.message}`, 'error');
                        riskFactors.push('GPS位置获取失败');
                    }
                );
            }
            
            // 检查IP地理位置
            fetch('https://ipapi.co/json/')
                .then(response => response.json())
                .then(data => {
                    detectionData.ipGeolocation = data;
                    
                    if (data.country_code === 'US') {
                        addResult(`✅ IP地理位置: ${data.city}, ${data.region}, ${data.country_name}`, 'success');
                    } else {
                        addResult(`❌ IP地理位置不在美国: ${data.country_name}`, 'error');
                        riskFactors.push('IP地理位置与声明国家不符');
                    }
                    
                    // 检查VPN/代理指标
                    if (data.org && (data.org.toLowerCase().includes('vpn') || 
                                   data.org.toLowerCase().includes('proxy') ||
                                   data.org.toLowerCase().includes('hosting'))) {
                        addResult(`⚠️ 检测到可能的VPN/代理: ${data.org}`, 'warning');
                        riskFactors.push('使用VPN/代理服务');
                    }
                })
                .catch(error => {
                    addResult('⚠️ 无法获取IP地理位置', 'warning');
                    riskFactors.push('IP地理位置检测失败');
                });
        }
        
        function checkTimezoneConsistency() {
            addResult('🕐 检查时区一致性...', 'info');
            
            const timezone = detectionData.timezone;
            const offset = detectionData.timezoneOffset;
            
            // 检查时区是否为美国时区
            const usTimezones = [
                'America/New_York', 'America/Chicago', 'America/Denver', 
                'America/Los_Angeles', 'America/Phoenix', 'America/Anchorage',
                'Pacific/Honolulu'
            ];
            
            if (usTimezones.includes(timezone)) {
                addResult(`✅ 时区设置正确: ${timezone}`, 'success');
            } else {
                addResult(`❌ 时区不是美国时区: ${timezone}`, 'error');
                riskFactors.push('时区与声明国家不符');
            }
            
            // 检查时区偏移是否合理
            const now = new Date();
            const expectedOffsets = {
                'America/New_York': [-300, -240],  // EST/EDT
                'America/Chicago': [-360, -300],   // CST/CDT
                'America/Denver': [-420, -360],    // MST/MDT
                'America/Los_Angeles': [-480, -420] // PST/PDT
            };
            
            if (expectedOffsets[timezone]) {
                const expected = expectedOffsets[timezone];
                if (expected.includes(offset)) {
                    addResult(`✅ 时区偏移正确: ${offset}分钟`, 'success');
                } else {
                    addResult(`⚠️ 时区偏移异常: 期望${expected}，实际${offset}`, 'warning');
                    riskFactors.push('时区偏移不一致');
                }
            }
        }
        
        function checkNetworkFingerprint() {
            addResult('🌐 检查网络指纹...', 'info');
            
            // 检查语言设置
            if (detectionData.language.startsWith('en-US')) {
                addResult(`✅ 主语言设置正确: ${detectionData.language}`, 'success');
            } else {
                addResult(`❌ 主语言不是en-US: ${detectionData.language}`, 'error');
                riskFactors.push('语言设置与声明国家不符');
            }
            
            // 检查User-Agent
            const ua = detectionData.userAgent;
            if (ua.includes('Windows') || ua.includes('Macintosh') || ua.includes('Linux')) {
                addResult('✅ User-Agent看起来正常', 'success');
            } else {
                addResult('⚠️ User-Agent可能异常', 'warning');
                riskFactors.push('User-Agent异常');
            }
            
            // 检查屏幕分辨率
            const resolution = detectionData.screenResolution;
            const commonResolutions = [
                '1920x1080', '1366x768', '1440x900', '1536x864', 
                '1280x720', '2560x1440', '1600x900'
            ];
            
            if (commonResolutions.includes(resolution)) {
                addResult(`✅ 屏幕分辨率常见: ${resolution}`, 'success');
            } else {
                addResult(`⚠️ 屏幕分辨率不常见: ${resolution}`, 'warning');
                riskFactors.push('屏幕分辨率异常');
            }
        }
        
        function checkBehaviorPatterns() {
            addResult('🎯 检查行为模式...', 'info');
            
            // 检查页面停留时间
            const pageLoadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            if (pageLoadTime > 2000) {
                addResult(`✅ 页面加载时间正常: ${pageLoadTime}ms`, 'success');
            } else {
                addResult(`⚠️ 页面加载时间过快: ${pageLoadTime}ms`, 'warning');
                riskFactors.push('页面加载时间异常');
            }
            
            // 检查鼠标移动
            let mouseMovements = 0;
            document.addEventListener('mousemove', () => mouseMovements++);
            
            setTimeout(() => {
                if (mouseMovements > 5) {
                    addResult(`✅ 检测到正常鼠标活动: ${mouseMovements}次移动`, 'success');
                } else {
                    addResult(`⚠️ 鼠标活动较少: ${mouseMovements}次移动`, 'warning');
                    riskFactors.push('缺乏人类行为特征');
                }
            }, 1000);
        }
        
        function calculateRiskScore() {
            addResult('📊 计算风险评分...', 'info');
            
            const baseScore = 0.1; // 基础风险分
            const riskPerFactor = 0.15; // 每个风险因素增加的分数
            
            const totalRisk = baseScore + (riskFactors.length * riskPerFactor);
            const riskScore = Math.min(totalRisk, 1.0); // 最高1.0
            
            const riskContainer = document.getElementById('risk-score-container');
            let riskClass, riskText, recommendation;
            
            if (riskScore < 0.3) {
                riskClass = 'risk-low';
                riskText = '低风险';
                recommendation = '✅ 注册可能会被接受';
            } else if (riskScore < 0.7) {
                riskClass = 'risk-medium';
                riskText = '中等风险';
                recommendation = '⚠️ 注册可能需要额外验证';
            } else {
                riskClass = 'risk-high';
                riskText = '高风险';
                recommendation = '❌ 注册很可能被拒绝';
            }
            
            riskContainer.innerHTML = `
                <div class="risk-score ${riskClass}">
                    风险评分: ${(riskScore * 100).toFixed(1)}% (${riskText})
                    <br>
                    <small>${recommendation}</small>
                </div>
            `;
            
            // 显示风险因素
            if (riskFactors.length > 0) {
                addResult(`⚠️ 发现 ${riskFactors.length} 个风险因素:`, 'warning');
                riskFactors.forEach(factor => {
                    addResult(`• ${factor}`, 'warning');
                });
            } else {
                addResult('✅ 未发现明显风险因素', 'success');
            }
            
            // 提供改进建议
            if (riskScore > 0.3) {
                addResult('💡 改进建议:', 'info');
                if (riskFactors.includes('GPS位置与声明国家不符')) {
                    addResult('• 检查扩展的地理位置伪装设置', 'info');
                }
                if (riskFactors.includes('时区与声明国家不符')) {
                    addResult('• 检查扩展的时区设置', 'info');
                }
                if (riskFactors.includes('语言设置与声明国家不符')) {
                    addResult('• 检查浏览器语言设置', 'info');
                }
                if (riskFactors.includes('使用VPN/代理服务')) {
                    addResult('• 考虑使用住宅IP或关闭VPN', 'info');
                }
            }
        }
        
        // 页面加载时自动填充一些测试数据
        window.addEventListener('load', () => {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('firstName').value = 'John';
            document.getElementById('lastName').value = 'Doe';
            document.getElementById('phone').value = '+****************';
            document.getElementById('state').value = 'California';
            document.getElementById('city').value = 'Los Angeles';
        });
    </script>
</body>
</html>
