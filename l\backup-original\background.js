// Global Timezone & Location Spoofer - Background Script v2.9.7
// 作者：小鱼游水 | 网址：https://xoxome.online

// 插件安装时的初始化
chrome.runtime.onInstalled.addListener(() => {
    console.log('🌍 Global Timezone Spoofer 已安装');
    console.log('🔧 版本: 2.9.7');
    console.log('👨‍💻 作者：小鱼游水 | 🌐 网址：https://xoxome.online');
    
    // 初始化默认设置
    chrome.storage.sync.get(['selectedRegion'], (result) => {
        if (!result.selectedRegion) {
            console.log('🔧 初始化默认设置');
            chrome.storage.sync.set({
                'selectedRegion': 'US',
                'lastUpdate': Date.now()
            });
        }
    });
});

// 监听存储变化，同步到所有标签页
chrome.storage.onChanged.addListener((changes, areaName) => {
    if (areaName === 'sync' && changes.selectedCity) {
        console.log('🔄 检测到地区设置变化，同步到所有标签页');
        
        // 获取所有标签页
        chrome.tabs.query({}, (tabs) => {
            const storageKey = 'globalTimezoneSpoofer_selectedCity';
            const newValue = changes.selectedCity.newValue;
            
            // 向每个标签页注入更新脚本
            tabs.forEach((tab) => {
                try {
                    // 使用 chrome.scripting.executeScript (Manifest V3)
                    chrome.scripting.executeScript({
                        target: { tabId: tab.id },
                        func: (key, value) => {
                            // 更新localStorage
                            localStorage.setItem(key, JSON.stringify(value));
                            console.log('🔄 时区设置已更新');
                        },
                        args: [storageKey, newValue]
                    }).catch(() => {
                        // 忽略无法注入的标签页（如chrome://页面）
                    });
                } catch (error) {
                    // 忽略错误，某些标签页可能无法访问
                }
            });
        });
    }
});

// 处理来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'updateTimezone') {
        console.log('📨 收到时区更新请求:', request.data);
        
        // 更新存储
        chrome.storage.sync.set({
            selectedCity: request.data,
            lastUpdate: Date.now()
        }, () => {
            console.log('✅ 时区设置已保存');
            sendResponse({ success: true });
        });
        
        return true; // 保持消息通道开放
    }
    
    if (request.action === 'getTimezone') {
        // 获取当前时区设置
        chrome.storage.sync.get(['selectedCity'], (result) => {
            sendResponse({ 
                success: true, 
                data: result.selectedCity || null 
            });
        });
        
        return true; // 保持消息通道开放
    }
});

// 处理标签页更新事件
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // 当页面完成加载时，确保时区设置已应用
    if (changeInfo.status === 'complete' && tab.url && !tab.url.startsWith('chrome://')) {
        chrome.storage.sync.get(['selectedCity'], (result) => {
            if (result.selectedCity) {
                try {
                    chrome.scripting.executeScript({
                        target: { tabId: tabId },
                        func: (cityData) => {
                            // 确保时区设置已应用
                            const storageKey = 'globalTimezoneSpoofer_selectedCity';
                            localStorage.setItem(storageKey, JSON.stringify(cityData));
                        },
                        args: [result.selectedCity]
                    }).catch(() => {
                        // 忽略无法注入的页面
                    });
                } catch (error) {
                    // 忽略错误
                }
            }
        });
    }
});

// 错误处理
chrome.runtime.onStartup.addListener(() => {
    console.log('🚀 Global Timezone Spoofer 启动');
});

// 处理插件图标点击（如果需要）
chrome.action.onClicked.addListener((tab) => {
    // 打开popup（这通常由manifest.json中的default_popup处理）
    console.log('🖱️ 插件图标被点击');
});