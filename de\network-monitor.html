<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络请求监控器 - Network Request Monitor</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .controls {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .url-input {
            flex: 1;
            min-width: 300px;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }
        
        .url-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background: #ff9800;
        }
        
        .btn-warning:hover {
            background: #f57c00;
        }
        
        .btn-danger {
            background: #f44336;
        }
        
        .btn-danger:hover {
            background: #d32f2f;
        }
        
        .monitor-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .monitor-panel {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .monitor-panel h3 {
            margin-top: 0;
            color: #fff;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .request-log {
            max-height: 400px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 10px;
        }
        
        .request-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 6px;
            margin: 5px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            border-left: 3px solid #4CAF50;
        }
        
        .request-item.suspicious {
            border-left-color: #ff9800;
            background: rgba(255, 152, 0, 0.1);
        }
        
        .request-item.blocked {
            border-left-color: #f44336;
            background: rgba(244, 67, 54, 0.1);
        }
        
        .request-details {
            margin-top: 5px;
            font-size: 11px;
            opacity: 0.8;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .analysis-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .filter-controls {
            margin-bottom: 15px;
        }
        
        .filter-controls label {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            margin-right: 15px;
            cursor: pointer;
        }
        
        .export-controls {
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌐 网络请求监控器</h1>
        <h2>Network Request Monitor for Registration Analysis</h2>
        <p>实时监控网络请求，检测反欺诈系统和注册拒绝原因</p>
    </div>
    
    <div class="controls">
        <input type="text" class="url-input" id="target-url" placeholder="输入要监控的网站URL (例如: https://example.com/register)">
        <button class="btn" id="start-monitor" onclick="startMonitoring()">开始监控</button>
        <button class="btn btn-warning" id="stop-monitor" onclick="stopMonitoring()" disabled>停止监控</button>
        <button class="btn btn-danger" onclick="clearLogs()">清除日志</button>
    </div>
    
    <div class="stats">
        <div class="stat-item">
            <div class="stat-number" id="total-requests">0</div>
            <div class="stat-label">总请求数</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="suspicious-requests">0</div>
            <div class="stat-label">可疑请求</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="blocked-requests">0</div>
            <div class="stat-label">被拦截</div>
        </div>
        <div class="stat-item">
            <div class="stat-number" id="fraud-services">0</div>
            <div class="stat-label">反欺诈服务</div>
        </div>
    </div>
    
    <div class="monitor-grid">
        <div class="monitor-panel">
            <h3>📡 实时请求日志</h3>
            <div class="filter-controls">
                <label><input type="checkbox" id="show-all" checked> 显示全部</label>
                <label><input type="checkbox" id="show-suspicious" checked> 可疑请求</label>
                <label><input type="checkbox" id="show-blocked" checked> 被拦截</label>
            </div>
            <div class="request-log" id="request-log"></div>
        </div>
        
        <div class="monitor-panel">
            <h3>🔍 反欺诈服务检测</h3>
            <div class="request-log" id="fraud-log"></div>
        </div>
    </div>
    
    <div class="analysis-panel">
        <h3>📊 请求分析</h3>
        <div id="analysis-results"></div>
        
        <div class="export-controls">
            <button class="btn" onclick="exportLogs()">📥 导出日志</button>
            <button class="btn" onclick="generateReport()">📋 生成报告</button>
        </div>
    </div>
    
    <script>
        let isMonitoring = false;
        let requestLogs = [];
        let fraudServices = [];
        let stats = {
            total: 0,
            suspicious: 0,
            blocked: 0,
            fraud: 0
        };
        
        // 反欺诈服务和可疑模式
        const fraudPatterns = [
            'verisoul', 'sift.com', 'riskified', 'signifyd', 'forter',
            'fraud', 'risk', 'verify', 'trust', 'security',
            'fingerprint', 'device', 'bot', 'captcha', 'recaptcha',
            'cloudflare', 'akamai', 'incapsula', 'distil', 'perimeterx',
            'maxmind', 'ipqualityscore', 'threatmetrix', 'iovation'
        ];
        
        const locationPatterns = [
            'ipapi', 'ip-api', 'ipinfo', 'geoip', 'ipgeolocation',
            'freegeoip', 'whatismyip', 'myip', 'checkip', 'getip',
            'location', 'geo', 'country', 'region', 'timezone'
        ];
        
        function startMonitoring() {
            const targetUrl = document.getElementById('target-url').value;
            if (!targetUrl) {
                alert('请输入要监控的网站URL');
                return;
            }
            
            isMonitoring = true;
            document.getElementById('start-monitor').disabled = true;
            document.getElementById('stop-monitor').disabled = false;
            
            addLog('request-log', `🎯 开始监控: ${targetUrl}`, 'normal');
            addLog('request-log', '📡 正在拦截网络请求...', 'normal');
            
            // 拦截fetch请求
            interceptFetch();
            
            // 拦截XMLHttpRequest
            interceptXHR();
            
            // 拦截WebSocket
            interceptWebSocket();
            
            // 监控页面变化
            monitorPageChanges();
        }
        
        function stopMonitoring() {
            isMonitoring = false;
            document.getElementById('start-monitor').disabled = false;
            document.getElementById('stop-monitor').disabled = true;
            
            addLog('request-log', '⏹️ 监控已停止', 'normal');
            generateAnalysis();
        }
        
        function clearLogs() {
            requestLogs = [];
            fraudServices = [];
            stats = { total: 0, suspicious: 0, blocked: 0, fraud: 0 };
            
            document.getElementById('request-log').innerHTML = '';
            document.getElementById('fraud-log').innerHTML = '';
            document.getElementById('analysis-results').innerHTML = '';
            updateStats();
        }
        
        function interceptFetch() {
            const originalFetch = window.fetch;
            window.fetch = function(input, init) {
                if (isMonitoring) {
                    const url = typeof input === 'string' ? input : input.url;
                    const method = init?.method || 'GET';
                    
                    logRequest('FETCH', method, url, init);
                }
                return originalFetch.apply(this, arguments);
            };
        }
        
        function interceptXHR() {
            const originalXHROpen = XMLHttpRequest.prototype.open;
            XMLHttpRequest.prototype.open = function(method, url) {
                if (isMonitoring) {
                    logRequest('XHR', method, url);
                }
                return originalXHROpen.apply(this, arguments);
            };
        }
        
        function interceptWebSocket() {
            const originalWebSocket = window.WebSocket;
            window.WebSocket = function(url, protocols) {
                if (isMonitoring) {
                    logRequest('WebSocket', 'CONNECT', url);
                }
                return new originalWebSocket(url, protocols);
            };
        }
        
        function logRequest(type, method, url, options = {}) {
            const timestamp = new Date().toISOString();
            const request = {
                type,
                method,
                url,
                options,
                timestamp
            };
            
            requestLogs.push(request);
            stats.total++;
            
            // 检查是否是可疑请求
            const isSuspicious = checkSuspicious(url);
            const isFraud = checkFraudService(url);
            const isBlocked = checkBlocked(url);
            
            if (isSuspicious) stats.suspicious++;
            if (isFraud) stats.fraud++;
            if (isBlocked) stats.blocked++;
            
            // 显示请求
            const requestClass = isBlocked ? 'blocked' : (isSuspicious || isFraud ? 'suspicious' : 'normal');
            const logContainer = isFraud ? 'fraud-log' : 'request-log';
            
            const displayText = `${type} ${method} ${url}`;
            const details = `时间: ${new Date(timestamp).toLocaleTimeString()}`;
            
            if (shouldShowRequest(requestClass)) {
                addLog(logContainer, displayText, requestClass, details);
            }
            
            updateStats();
        }
        
        function checkSuspicious(url) {
            return fraudPatterns.some(pattern => url.toLowerCase().includes(pattern)) ||
                   locationPatterns.some(pattern => url.toLowerCase().includes(pattern));
        }
        
        function checkFraudService(url) {
            const fraudServicePatterns = [
                'verisoul', 'sift.com', 'riskified', 'signifyd', 'forter',
                'threatmetrix', 'iovation', 'maxmind', 'ipqualityscore'
            ];
            
            const isFraud = fraudServicePatterns.some(pattern => url.toLowerCase().includes(pattern));
            if (isFraud && !fraudServices.includes(url)) {
                fraudServices.push(url);
            }
            return isFraud;
        }
        
        function checkBlocked(url) {
            // 检查是否被扩展拦截
            return url.includes('verisoul.ai') || 
                   url.includes('ipapi.co') ||
                   url.includes('ip-api.com');
        }
        
        function shouldShowRequest(requestClass) {
            const showAll = document.getElementById('show-all').checked;
            const showSuspicious = document.getElementById('show-suspicious').checked;
            const showBlocked = document.getElementById('show-blocked').checked;
            
            if (showAll) return true;
            if (requestClass === 'suspicious' && showSuspicious) return true;
            if (requestClass === 'blocked' && showBlocked) return true;
            
            return false;
        }
        
        function addLog(containerId, message, type = 'normal', details = '') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `request-item ${type}`;
            
            let icon = '📡';
            if (type === 'suspicious') icon = '⚠️';
            if (type === 'blocked') icon = '🛡️';
            
            div.innerHTML = `
                ${icon} ${message}
                ${details ? `<div class="request-details">${details}</div>` : ''}
            `;
            
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }
        
        function updateStats() {
            document.getElementById('total-requests').textContent = stats.total;
            document.getElementById('suspicious-requests').textContent = stats.suspicious;
            document.getElementById('blocked-requests').textContent = stats.blocked;
            document.getElementById('fraud-services').textContent = stats.fraud;
        }
        
        function monitorPageChanges() {
            // 监控DOM变化
            const observer = new MutationObserver((mutations) => {
                if (!isMonitoring) return;
                
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === Node.ELEMENT_NODE) {
                                // 检查新添加的脚本
                                if (node.tagName === 'SCRIPT' && node.src) {
                                    logRequest('SCRIPT', 'LOAD', node.src);
                                }
                                
                                // 检查新添加的iframe
                                if (node.tagName === 'IFRAME' && node.src) {
                                    logRequest('IFRAME', 'LOAD', node.src);
                                }
                            }
                        });
                    }
                });
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
        
        function generateAnalysis() {
            const analysisContainer = document.getElementById('analysis-results');
            
            let analysis = '<h4>📊 监控分析结果</h4>';
            
            // 总体统计
            analysis += `
                <div style="margin: 15px 0;">
                    <strong>总体统计:</strong><br>
                    • 总请求数: ${stats.total}<br>
                    • 可疑请求: ${stats.suspicious}<br>
                    • 被拦截请求: ${stats.blocked}<br>
                    • 反欺诈服务: ${stats.fraud}
                </div>
            `;
            
            // 检测到的反欺诈服务
            if (fraudServices.length > 0) {
                analysis += `
                    <div style="margin: 15px 0;">
                        <strong>🚨 检测到的反欺诈服务:</strong><br>
                        ${fraudServices.map(service => `• ${service}`).join('<br>')}
                    </div>
                `;
            }
            
            // 风险评估
            const riskScore = calculateRiskScore();
            const riskLevel = riskScore > 0.7 ? '高风险' : riskScore > 0.4 ? '中等风险' : '低风险';
            const riskColor = riskScore > 0.7 ? '#f44336' : riskScore > 0.4 ? '#ff9800' : '#4CAF50';
            
            analysis += `
                <div style="margin: 15px 0; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 8px;">
                    <strong>🎯 风险评估:</strong><br>
                    <div style="color: ${riskColor}; font-size: 18px; font-weight: bold;">
                        风险等级: ${riskLevel} (${(riskScore * 100).toFixed(1)}%)
                    </div>
                </div>
            `;
            
            // 建议
            analysis += generateRecommendations(riskScore);
            
            analysisContainer.innerHTML = analysis;
        }
        
        function calculateRiskScore() {
            let score = 0;
            
            // 基于检测到的反欺诈服务数量
            score += Math.min(fraudServices.length * 0.2, 0.6);
            
            // 基于可疑请求比例
            if (stats.total > 0) {
                const suspiciousRatio = stats.suspicious / stats.total;
                score += suspiciousRatio * 0.3;
            }
            
            // 基于被拦截请求数量
            score += Math.min(stats.blocked * 0.1, 0.3);
            
            return Math.min(score, 1.0);
        }
        
        function generateRecommendations(riskScore) {
            let recommendations = '<div style="margin: 15px 0;"><strong>💡 改进建议:</strong><br>';
            
            if (fraudServices.length > 0) {
                recommendations += '• 网站使用了高级反欺诈系统，建议检查扩展的拦截功能<br>';
            }
            
            if (stats.blocked > 0) {
                recommendations += '• 扩展正在拦截部分请求，这可能被检测到<br>';
            }
            
            if (riskScore > 0.5) {
                recommendations += '• 建议使用更真实的网络环境和行为模式<br>';
                recommendations += '• 考虑分批次进行注册，避免批量操作<br>';
            }
            
            recommendations += '</div>';
            return recommendations;
        }
        
        function exportLogs() {
            const data = {
                timestamp: new Date().toISOString(),
                stats: stats,
                fraudServices: fraudServices,
                requests: requestLogs
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `network-monitor-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        function generateReport() {
            const report = `
网络请求监控报告
生成时间: ${new Date().toLocaleString()}

=== 总体统计 ===
总请求数: ${stats.total}
可疑请求: ${stats.suspicious}
被拦截请求: ${stats.blocked}
反欺诈服务: ${stats.fraud}

=== 检测到的反欺诈服务 ===
${fraudServices.length > 0 ? fraudServices.join('\n') : '无'}

=== 风险评估 ===
风险评分: ${(calculateRiskScore() * 100).toFixed(1)}%

=== 详细请求日志 ===
${requestLogs.map(req => `${req.timestamp} - ${req.type} ${req.method} ${req.url}`).join('\n')}
            `;
            
            const blob = new Blob([report], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `network-report-${Date.now()}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // 过滤器事件监听
        document.getElementById('show-all').addEventListener('change', refreshDisplay);
        document.getElementById('show-suspicious').addEventListener('change', refreshDisplay);
        document.getElementById('show-blocked').addEventListener('change', refreshDisplay);
        
        function refreshDisplay() {
            // 重新显示所有请求
            document.getElementById('request-log').innerHTML = '';
            document.getElementById('fraud-log').innerHTML = '';
            
            requestLogs.forEach(req => {
                const isSuspicious = checkSuspicious(req.url);
                const isFraud = checkFraudService(req.url);
                const isBlocked = checkBlocked(req.url);
                
                const requestClass = isBlocked ? 'blocked' : (isSuspicious || isFraud ? 'suspicious' : 'normal');
                const logContainer = isFraud ? 'fraud-log' : 'request-log';
                
                if (shouldShowRequest(requestClass)) {
                    const displayText = `${req.type} ${req.method} ${req.url}`;
                    const details = `时间: ${new Date(req.timestamp).toLocaleTimeString()}`;
                    addLog(logContainer, displayText, requestClass, details);
                }
            });
        }
    </script>
</body>
</html>
