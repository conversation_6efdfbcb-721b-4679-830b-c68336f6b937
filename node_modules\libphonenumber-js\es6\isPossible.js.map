{"version": 3, "file": "isPossible.js", "names": ["<PERSON><PERSON><PERSON>", "checkNumberLength", "isPossiblePhoneNumber", "input", "options", "metadata", "undefined", "v2", "countryCallingCode", "Error", "selectNumberingPlan", "phone", "country", "hasCountry", "possibleLengths", "isPossibleNumber", "nationalNumber", "isNonGeographicCallingCode"], "sources": ["../source/isPossible.js"], "sourcesContent": ["import Metadata from './metadata.js'\r\nimport checkNumberLength from './helpers/checkNumberLength.js'\r\n\r\n/**\r\n * Checks if a phone number is \"possible\" (basically just checks its length).\r\n *\r\n * isPossible(phoneNumberInstance, { ..., v2: true }, metadata)\r\n *\r\n * isPossible({ phone: '8005553535', country: 'RU' }, { ... }, metadata)\r\n * isPossible({ phone: '8005553535', country: 'RU' }, undefined, metadata)\r\n *\r\n * @param  {object|PhoneNumber} input — If `options.v2: true` flag is passed, the `input` should be a `PhoneNumber` instance. Otherwise, it should be an object of shape `{ phone: '...', country: '...' }`.\r\n * @param  {object} [options]\r\n * @param  {object} metadata\r\n * @return {string}\r\n */\r\nexport default function isPossiblePhoneNumber(input, options, metadata) {\r\n\t/* istanbul ignore if */\r\n\tif (options === undefined) {\r\n\t\toptions = {}\r\n\t}\r\n\r\n\tmetadata = new Metadata(metadata)\r\n\r\n\tif (options.v2) {\r\n\t\tif (!input.countryCallingCode) {\r\n\t\t\tthrow new Error('Invalid phone number object passed')\r\n\t\t}\r\n\t\tmetadata.selectNumberingPlan(input.countryCallingCode)\r\n\t} else {\r\n\t\tif (!input.phone) {\r\n\t\t\treturn false\r\n\t\t}\r\n\t\tif (input.country) {\r\n\t\t\tif (!metadata.hasCountry(input.country)) {\r\n\t\t\t\tthrow new Error(`Unknown country: ${input.country}`)\r\n\t\t\t}\r\n\t\t\tmetadata.country(input.country)\r\n\t\t} else {\r\n\t\t\tif (!input.countryCallingCode) {\r\n\t\t\t\tthrow new Error('Invalid phone number object passed')\r\n\t\t\t}\r\n\t\t\tmetadata.selectNumberingPlan(input.countryCallingCode)\r\n\t\t}\r\n\t}\r\n\r\n\t// Old metadata (< 1.0.18) had no \"possible length\" data.\r\n\tif (metadata.possibleLengths()) {\r\n\t\treturn isPossibleNumber(input.phone || input.nationalNumber, metadata)\r\n\t} else {\r\n\t\t// There was a bug between `1.7.35` and `1.7.37` where \"possible_lengths\"\r\n\t\t// were missing for \"non-geographical\" numbering plans.\r\n\t\t// Just assume the number is possible in such cases:\r\n\t\t// it's unlikely that anyone generated their custom metadata\r\n\t\t// in that short period of time (one day).\r\n\t\t// This code can be removed in some future major version update.\r\n\t\tif (input.countryCallingCode && metadata.isNonGeographicCallingCode(input.countryCallingCode)) {\r\n\t\t\t// \"Non-geographic entities\" did't have `possibleLengths`\r\n\t\t\t// due to a bug in metadata generation process.\r\n\t\t\treturn true\r\n\t\t} else {\r\n\t\t\tthrow new Error('Missing \"possibleLengths\" in metadata. Perhaps the metadata has been generated before v1.0.18.');\r\n\t\t}\r\n\t}\r\n}\r\n\r\nexport function isPossibleNumber(nationalNumber, metadata) { //, isInternational) {\r\n\tswitch (checkNumberLength(nationalNumber, metadata)) {\r\n\t\tcase 'IS_POSSIBLE':\r\n\t\t\treturn true\r\n\t\t// This library ignores \"local-only\" phone numbers (for simplicity).\r\n\t\t// See the readme for more info on what are \"local-only\" phone numbers.\r\n\t\t// case 'IS_POSSIBLE_LOCAL_ONLY':\r\n\t\t// \treturn !isInternational\r\n\t\tdefault:\r\n\t\t\treturn false\r\n\t}\r\n}"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,eAArB;AACA,OAAOC,iBAAP,MAA8B,gCAA9B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASC,qBAAT,CAA+BC,KAA/B,EAAsCC,OAAtC,EAA+CC,QAA/C,EAAyD;EACvE;EACA,IAAID,OAAO,KAAKE,SAAhB,EAA2B;IAC1BF,OAAO,GAAG,EAAV;EACA;;EAEDC,QAAQ,GAAG,IAAIL,QAAJ,CAAaK,QAAb,CAAX;;EAEA,IAAID,OAAO,CAACG,EAAZ,EAAgB;IACf,IAAI,CAACJ,KAAK,CAACK,kBAAX,EAA+B;MAC9B,MAAM,IAAIC,KAAJ,CAAU,oCAAV,CAAN;IACA;;IACDJ,QAAQ,CAACK,mBAAT,CAA6BP,KAAK,CAACK,kBAAnC;EACA,CALD,MAKO;IACN,IAAI,CAACL,KAAK,CAACQ,KAAX,EAAkB;MACjB,OAAO,KAAP;IACA;;IACD,IAAIR,KAAK,CAACS,OAAV,EAAmB;MAClB,IAAI,CAACP,QAAQ,CAACQ,UAAT,CAAoBV,KAAK,CAACS,OAA1B,CAAL,EAAyC;QACxC,MAAM,IAAIH,KAAJ,4BAA8BN,KAAK,CAACS,OAApC,EAAN;MACA;;MACDP,QAAQ,CAACO,OAAT,CAAiBT,KAAK,CAACS,OAAvB;IACA,CALD,MAKO;MACN,IAAI,CAACT,KAAK,CAACK,kBAAX,EAA+B;QAC9B,MAAM,IAAIC,KAAJ,CAAU,oCAAV,CAAN;MACA;;MACDJ,QAAQ,CAACK,mBAAT,CAA6BP,KAAK,CAACK,kBAAnC;IACA;EACD,CA5BsE,CA8BvE;;;EACA,IAAIH,QAAQ,CAACS,eAAT,EAAJ,EAAgC;IAC/B,OAAOC,gBAAgB,CAACZ,KAAK,CAACQ,KAAN,IAAeR,KAAK,CAACa,cAAtB,EAAsCX,QAAtC,CAAvB;EACA,CAFD,MAEO;IACN;IACA;IACA;IACA;IACA;IACA;IACA,IAAIF,KAAK,CAACK,kBAAN,IAA4BH,QAAQ,CAACY,0BAAT,CAAoCd,KAAK,CAACK,kBAA1C,CAAhC,EAA+F;MAC9F;MACA;MACA,OAAO,IAAP;IACA,CAJD,MAIO;MACN,MAAM,IAAIC,KAAJ,CAAU,gGAAV,CAAN;IACA;EACD;AACD;AAED,OAAO,SAASM,gBAAT,CAA0BC,cAA1B,EAA0CX,QAA1C,EAAoD;EAAE;EAC5D,QAAQJ,iBAAiB,CAACe,cAAD,EAAiBX,QAAjB,CAAzB;IACC,KAAK,aAAL;MACC,OAAO,IAAP;IACD;IACA;IACA;IACA;;IACA;MACC,OAAO,KAAP;EARF;AAUA"}