<?php
// 配置购买链接管理页面
header('Content-Type: text/html; charset=utf-8');
require_once 'config.php';

$pdo = getDBConnection();
if (!$pdo) {
    die('数据库连接失败');
}

// 处理表单提交
if ($_POST) {
    $purchaseLink = trim($_POST['purchase_link'] ?? '');
    
    if ($purchaseLink) {
        try {
            // 更新或插入购买链接配置
            $stmt = $pdo->prepare("
                INSERT INTO system_config (config_key, config_value, description) 
                VALUES ('purchase_link', ?, '激活码购买链接') 
                ON DUPLICATE KEY UPDATE 
                config_value = VALUES(config_value), 
                updated_at = NOW()
            ");
            $stmt->execute([$purchaseLink]);
            $message = "购买链接配置成功！";
            $messageType = "success";
        } catch (Exception $e) {
            $message = "配置失败：" . $e->getMessage();
            $messageType = "error";
        }
    } else {
        $message = "请输入有效的购买链接";
        $messageType = "error";
    }
}

// 获取当前配置
$currentLink = getSystemConfig($pdo, 'purchase_link') ?: '';

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购买链接配置</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        input[type="url"], input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        input[type="url"]:focus, input[type="text"]:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .current-config {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .current-config h3 {
            margin-top: 0;
            color: #495057;
        }
        .current-link {
            word-break: break-all;
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        .test-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .test-btn {
            background: #2196F3;
            margin-top: 10px;
        }
        .test-btn:hover {
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
        }
        .examples {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .examples h4 {
            margin-top: 0;
            color: #856404;
        }
        .examples ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .examples li {
            margin: 5px 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 购买链接配置</h1>
        
        <?php if (isset($message)): ?>
            <div class="message <?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <div class="current-config">
            <h3>📋 当前配置</h3>
            <div class="current-link">
                <?php echo $currentLink ? htmlspecialchars($currentLink) : '未配置'; ?>
            </div>
        </div>
        
        <form method="POST">
            <div class="form-group">
                <label for="purchase_link">购买链接 URL：</label>
                <input 
                    type="url" 
                    id="purchase_link" 
                    name="purchase_link" 
                    value="<?php echo htmlspecialchars($currentLink); ?>"
                    placeholder="https://example.com/buy-activation-code"
                    required
                >
            </div>
            
            <button type="submit" class="btn">💾 保存配置</button>
        </form>
        
        <div class="test-section">
            <h3>🧪 测试功能</h3>
            <p>配置完成后，可以测试购买提示功能：</p>
            <button class="btn test-btn" onclick="testExpiredUser()">
                测试过期用户提示
            </button>
        </div>
        
        <div class="examples">
            <h4>💡 配置示例</h4>
            <ul>
                <li>淘宝店铺：https://shop123456.taobao.com/</li>
                <li>微店：https://weidian.com/item.html?itemID=123456</li>
                <li>自建商城：https://yourstore.com/activation-codes</li>
                <li>支付页面：https://pay.example.com/buy?product=activation</li>
            </ul>
            <p><strong>注意：</strong>确保链接可以正常访问，用户点击后能够购买到激活码。</p>
        </div>
    </div>

    <script>
        function testExpiredUser() {
            // 创建一个测试用户数据
            const testUserData = {
                username: '测试用户',
                remaining_days: 0,
                status: 'expired',
                purchase_link: document.getElementById('purchase_link').value || '#'
            };
            
            // 在新窗口中打开测试页面
            const testWindow = window.open('', '_blank', 'width=400,height=600');
            testWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>过期用户提示测试</title>
                    <style>
                        body {
                            margin: 0;
                            padding: 20px;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            text-align: center;
                        }
                        .expired-notice {
                            background: linear-gradient(135deg, #ff6b6b, #ff8e53);
                            border-radius: 15px;
                            padding: 30px;
                            margin: 20px;
                            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                        }
                        .notice-icon {
                            font-size: 48px;
                            margin-bottom: 20px;
                        }
                        h2 { margin: 0 0 15px 0; }
                        p { margin: 10px 0; opacity: 0.9; }
                        .purchase-btn {
                            background: rgba(255, 255, 255, 0.2);
                            color: white;
                            border: 2px solid rgba(255, 255, 255, 0.3);
                            padding: 12px 25px;
                            border-radius: 25px;
                            font-size: 16px;
                            cursor: pointer;
                            margin: 15px 5px;
                            transition: all 0.3s ease;
                        }
                        .purchase-btn:hover {
                            background: rgba(255, 255, 255, 0.3);
                            transform: translateY(-2px);
                        }
                    </style>
                </head>
                <body>
                    <div class="expired-notice">
                        <div class="notice-icon">⏰</div>
                        <h2>试用期已结束</h2>
                        <p>用户：<strong>${testUserData.username}</strong></p>
                        <p>剩余天数：<strong style="color: #ffcdd2;">0天</strong></p>
                        <p>感谢您使用我们的插件！<br>请购买激活码继续享受服务</p>
                        <button class="purchase-btn" onclick="window.open('${testUserData.purchase_link}', '_blank')">
                            🛒 立即购买激活码
                        </button>
                        <button class="purchase-btn" onclick="window.close()">
                            关闭测试
                        </button>
                    </div>
                    <p style="font-size: 12px; opacity: 0.7; margin-top: 20px;">
                        这是购买提示界面的预览效果
                    </p>
                </body>
                </html>
            `);
        }
    </script>
</body>
</html>
