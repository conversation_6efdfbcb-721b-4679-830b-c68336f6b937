<?php
// 登录测试工具
header('Content-Type: text/html; charset=utf-8');
require_once 'config.php';

$result = null;
$error = null;

if ($_POST && isset($_POST['username']) && isset($_POST['password'])) {
    $username = trim($_POST['username']);
    $password = trim($_POST['password']);
    
    try {
        // 模拟登录API调用
        $postData = json_encode([
            'username' => $username,
            'password' => $password
        ]);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://103.96.75.196/api/login.php');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($postData)
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response === false) {
            throw new Exception('请求失败');
        }
        
        $result = [
            'http_code' => $httpCode,
            'response' => json_decode($response, true),
            'raw_response' => $response
        ];
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// 获取用户列表用于测试
$pdo = getDBConnection();
$users = [];
if ($pdo) {
    $stmt = $pdo->prepare("
        SELECT username, status, remaining_days, created_at
        FROM users
        ORDER BY created_at DESC
        LIMIT 10
    ");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        .json {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .users-list {
            margin-top: 30px;
            padding: 20px;
            background: #e3f2fd;
            border-radius: 8px;
        }
        .user-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 5px;
            cursor: pointer;
        }
        .user-item:hover {
            background: #f0f0f0;
        }
        .user-info {
            flex: 1;
        }
        .user-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .user-status.active {
            background: #d4edda;
            color: #155724;
        }
        .user-status.expired {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 登录测试工具</h1>
        
        <form method="POST">
            <div class="form-group">
                <label for="username">用户名：</label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                    placeholder="输入用户名"
                    required
                >
            </div>
            
            <div class="form-group">
                <label for="password">密码：</label>
                <input 
                    type="password" 
                    id="password" 
                    name="password" 
                    placeholder="输入密码"
                    required
                >
            </div>
            
            <button type="submit" class="btn">🚀 测试登录</button>
        </form>
        
        <?php if ($error): ?>
            <div class="result error">
                <h3>❌ 请求错误</h3>
                <p><?php echo htmlspecialchars($error); ?></p>
            </div>
        <?php endif; ?>
        
        <?php if ($result): ?>
            <div class="result <?php echo $result['response']['success'] ? 'success' : 'error'; ?>">
                <h3><?php echo $result['response']['success'] ? '✅ 登录成功' : '❌ 登录失败'; ?></h3>
                
                <p><strong>HTTP状态码：</strong> <?php echo $result['http_code']; ?></p>
                <p><strong>响应消息：</strong> <?php echo htmlspecialchars($result['response']['message'] ?? '无消息'); ?></p>
                
                <?php if (isset($result['response']['data'])): ?>
                    <h4>📊 响应数据：</h4>
                    <div class="json"><?php echo json_encode($result['response']['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></div>
                <?php endif; ?>
                
                <h4>🔍 完整响应：</h4>
                <div class="json"><?php echo htmlspecialchars($result['raw_response']); ?></div>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($users)): ?>
            <div class="users-list">
                <h3>👥 可用测试用户（点击填入用户名）</h3>
                <?php foreach ($users as $user): ?>
                    <div class="user-item" onclick="fillUsername('<?php echo htmlspecialchars($user['username']); ?>')">
                        <div class="user-info">
                            <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                            <br>
                            <small>注册时间：<?php echo date('Y-m-d', strtotime($user['created_at'])); ?></small>
                        </div>
                        <div>
                            <span class="user-status <?php echo $user['status']; ?>">
                                <?php echo $user['status']; ?>
                            </span>
                            <br>
                            <small><?php echo $user['remaining_days']; ?>天</small>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <div style="margin-top: 30px; padding: 20px; background: #fff3cd; border-radius: 8px;">
            <h3>💡 测试说明</h3>
            <ul>
                <li>此工具直接调用登录API进行测试</li>
                <li>可以测试不同状态用户的登录响应</li>
                <li>查看完整的API响应数据</li>
                <li>点击用户列表可快速填入用户名</li>
                <li>密码通常是注册时设置的密码</li>
            </ul>
        </div>
    </div>

    <script>
        function fillUsername(username) {
            document.getElementById('username').value = username;
        }
    </script>
</body>
</html>
