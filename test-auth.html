<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件授权测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            font-weight: bold;
        }
        .authorized {
            background: rgba(76, 175, 80, 0.3);
            border: 2px solid #4CAF50;
        }
        .unauthorized {
            background: rgba(244, 67, 54, 0.3);
            border: 2px solid #f44336;
        }
        .info {
            background: rgba(33, 150, 243, 0.3);
            border: 2px solid #2196F3;
        }
        button {
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }
        .test-results {
            margin-top: 20px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }
        pre {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 插件授权测试页面</h1>
        <p>此页面用于测试插件的授权验证系统是否正常工作。</p>
        
        <div id="authStatus" class="status unauthorized">
            🔒 检查中...
        </div>
        
        <div class="test-results">
            <h3>测试结果：</h3>
            <div id="testResults">
                <p>正在检查授权状态...</p>
            </div>
        </div>
        
        <div>
            <button onclick="checkAuth()">🔍 重新检查授权</button>
            <button onclick="testPluginFeatures()">🧪 测试插件功能</button>
            <button onclick="clearAuthData()">🗑️ 清除授权数据</button>
        </div>
        
        <div class="test-results">
            <h3>详细信息：</h3>
            <pre id="detailInfo">等待检查...</pre>
        </div>
    </div>

    <script>
        // 页面加载时自动检查授权状态
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
        });

        function checkAuth() {
            const statusDiv = document.getElementById('authStatus');
            const resultsDiv = document.getElementById('testResults');
            const detailDiv = document.getElementById('detailInfo');
            
            let results = [];
            let details = [];
            
            // 检查window对象中的授权标记
            if (window.PLUGIN_AUTHORIZED === true && window.PLUGIN_USER_INFO) {
                statusDiv.className = 'status authorized';
                statusDiv.innerHTML = '✅ 插件已授权';
                results.push('✅ Window对象授权检查: 通过');
                details.push(`用户: ${window.PLUGIN_USER_INFO.username}`);
                details.push(`剩余天数: ${window.PLUGIN_USER_INFO.remaining_days}`);
                details.push(`状态: ${window.PLUGIN_USER_INFO.status}`);
                details.push(`授权时间: ${window.PLUGIN_USER_INFO.authorized_at}`);
            } else {
                statusDiv.className = 'status unauthorized';
                statusDiv.innerHTML = '🔒 插件未授权';
                results.push('❌ Window对象授权检查: 失败');
            }
            
            // 检查localStorage中的授权信息
            const authStatus = localStorage.getItem('plugin_auth_status');
            const userInfo = localStorage.getItem('plugin_user_info');
            
            if (authStatus === 'authorized' && userInfo) {
                results.push('✅ LocalStorage授权检查: 通过');
                try {
                    const userData = JSON.parse(userInfo);
                    details.push(`LocalStorage用户: ${userData.username}`);
                } catch (e) {
                    results.push('⚠️ LocalStorage数据解析失败');
                }
            } else {
                results.push('❌ LocalStorage授权检查: 失败');
            }
            
            // 检查插件功能是否被加载
            if (typeof window.GLOBAL_CITIES_DATABASE !== 'undefined') {
                results.push('✅ 插件核心功能: 已加载');
            } else {
                results.push('❌ 插件核心功能: 未加载');
            }
            
            // 显示结果
            resultsDiv.innerHTML = results.map(r => `<p>${r}</p>`).join('');
            detailDiv.textContent = details.join('\n') || '无详细信息';
        }

        function testPluginFeatures() {
            const results = [];
            
            // 测试时区功能
            const now = new Date();
            results.push(`当前时间: ${now.toString()}`);
            results.push(`时区偏移: ${now.getTimezoneOffset()} 分钟`);
            
            // 测试地理位置功能
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        results.push(`地理位置: ${position.coords.latitude}, ${position.coords.longitude}`);
                        document.getElementById('detailInfo').textContent = results.join('\n');
                    },
                    (error) => {
                        results.push(`地理位置错误: ${error.message}`);
                        document.getElementById('detailInfo').textContent = results.join('\n');
                    }
                );
            } else {
                results.push('地理位置API不可用');
            }
            
            // 测试语言设置
            results.push(`浏览器语言: ${navigator.language}`);
            results.push(`语言列表: ${navigator.languages.join(', ')}`);
            
            document.getElementById('detailInfo').textContent = results.join('\n');
        }

        function clearAuthData() {
            if (confirm('确定要清除所有授权数据吗？这将禁用插件功能。')) {
                // 清除window对象
                window.PLUGIN_AUTHORIZED = false;
                delete window.PLUGIN_USER_INFO;
                
                // 清除localStorage
                localStorage.removeItem('plugin_auth_status');
                localStorage.removeItem('plugin_user_info');
                
                alert('授权数据已清除，页面将刷新');
                location.reload();
            }
        }
    </script>
</body>
</html>
