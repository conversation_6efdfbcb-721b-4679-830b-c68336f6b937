{"version": 3, "file": "semver-compare.js", "names": ["a", "b", "split", "pa", "pb", "i", "na", "Number", "nb", "isNaN"], "sources": ["../../source/tools/semver-compare.js"], "sourcesContent": ["// Copy-pasted from:\r\n// https://github.com/substack/semver-compare/blob/master/index.js\r\n//\r\n// Inlining this function because some users reported issues with\r\n// importing from `semver-compare` in a browser with ES6 \"native\" modules.\r\n//\r\n// Fixes `semver-compare` not being able to compare versions with alpha/beta/etc \"tags\".\r\n// https://github.com/catamphetamine/libphonenumber-js/issues/381\r\nexport default function(a, b) {\r\n    a = a.split('-')\r\n    b = b.split('-')\r\n    var pa = a[0].split('.')\r\n    var pb = b[0].split('.')\r\n    for (var i = 0; i < 3; i++) {\r\n        var na = Number(pa[i])\r\n        var nb = Number(pb[i])\r\n        if (na > nb) return 1\r\n        if (nb > na) return -1\r\n        if (!isNaN(na) && isNaN(nb)) return 1\r\n        if (isNaN(na) && !isNaN(nb)) return -1\r\n    }\r\n    if (a[1] && b[1]) {\r\n        return a[1] > b[1] ? 1 : (a[1] < b[1] ? -1 : 0)\r\n    }\r\n    return !a[1] && b[1] ? 1 : (a[1] && !b[1] ? -1 : 0)\r\n}"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,kBAASA,CAAT,EAAYC,CAAZ,EAAe;EAC1BD,CAAC,GAAGA,CAAC,CAACE,KAAF,CAAQ,GAAR,CAAJ;EACAD,CAAC,GAAGA,CAAC,CAACC,KAAF,CAAQ,GAAR,CAAJ;EACA,IAAIC,EAAE,GAAGH,CAAC,CAAC,CAAD,CAAD,CAAKE,KAAL,CAAW,GAAX,CAAT;EACA,IAAIE,EAAE,GAAGH,CAAC,CAAC,CAAD,CAAD,CAAKC,KAAL,CAAW,GAAX,CAAT;;EACA,KAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,CAApB,EAAuBA,CAAC,EAAxB,EAA4B;IACxB,IAAIC,EAAE,GAAGC,MAAM,CAACJ,EAAE,CAACE,CAAD,CAAH,CAAf;IACA,IAAIG,EAAE,GAAGD,MAAM,CAACH,EAAE,CAACC,CAAD,CAAH,CAAf;IACA,IAAIC,EAAE,GAAGE,EAAT,EAAa,OAAO,CAAP;IACb,IAAIA,EAAE,GAAGF,EAAT,EAAa,OAAO,CAAC,CAAR;IACb,IAAI,CAACG,KAAK,CAACH,EAAD,CAAN,IAAcG,KAAK,CAACD,EAAD,CAAvB,EAA6B,OAAO,CAAP;IAC7B,IAAIC,KAAK,CAACH,EAAD,CAAL,IAAa,CAACG,KAAK,CAACD,EAAD,CAAvB,EAA6B,OAAO,CAAC,CAAR;EAChC;;EACD,IAAIR,CAAC,CAAC,CAAD,CAAD,IAAQC,CAAC,CAAC,CAAD,CAAb,EAAkB;IACd,OAAOD,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAC,CAAC,CAAD,CAAR,GAAc,CAAd,GAAmBD,CAAC,CAAC,CAAD,CAAD,GAAOC,CAAC,CAAC,CAAD,CAAR,GAAc,CAAC,CAAf,GAAmB,CAA7C;EACH;;EACD,OAAO,CAACD,CAAC,CAAC,CAAD,CAAF,IAASC,CAAC,CAAC,CAAD,CAAV,GAAgB,CAAhB,GAAqBD,CAAC,CAAC,CAAD,CAAD,IAAQ,CAACC,CAAC,CAAC,CAAD,CAAV,GAAgB,CAAC,CAAjB,GAAqB,CAAjD;AACH"}