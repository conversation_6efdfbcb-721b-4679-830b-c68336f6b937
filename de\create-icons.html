<!DOCTYPE html>
<html>
<head>
    <title>Create Extension Icons</title>
</head>
<body>
    <h1>Extension Icon Generator</h1>
    <canvas id="canvas16" width="16" height="16"></canvas>
    <canvas id="canvas48" width="48" height="48"></canvas>
    <canvas id="canvas128" width="128" height="128"></canvas>
    
    <script>
        function createIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Globe emoji (simplified)
            ctx.fillStyle = '#ffffff';
            ctx.font = `${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('🌍', size/2, size/2);
            
            // Download link
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL();
            link.textContent = `Download ${size}x${size} icon`;
            link.style.display = 'block';
            link.style.margin = '10px';
            document.body.appendChild(link);
        }
        
        // Create all icons
        createIcon(document.getElementById('canvas16'), 16);
        createIcon(document.getElementById('canvas48'), 48);
        createIcon(document.getElementById('canvas128'), 128);
    </script>
</body>
</html>
