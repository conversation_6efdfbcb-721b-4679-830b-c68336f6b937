{"version": 3, "file": "isViablePhoneNumber.js", "names": ["MIN_LENGTH_FOR_NSN", "VALID_DIGITS", "VALID_PUNCTUATION", "PLUS_CHARS", "createExtensionPattern", "MIN_LENGTH_PHONE_NUMBER_PATTERN", "VALID_PHONE_NUMBER", "VALID_PHONE_NUMBER_START_REG_EXP", "RegExp", "VALID_PHONE_NUMBER_WITH_EXTENSION", "VALID_PHONE_NUMBER_PATTERN", "isViablePhoneNumber", "number", "length", "test", "isViablePhoneNumberStart"], "sources": ["../../source/helpers/isViablePhoneNumber.js"], "sourcesContent": ["import {\r\n\tMIN_LENGTH_FOR_NSN,\r\n\tVALID_DIGITS,\r\n\tVALID_PUNCTUATION,\r\n\tPLUS_CHARS\r\n} from '../constants.js'\r\n\r\nimport createExtensionPattern from './extension/createExtensionPattern.js'\r\n\r\n//  Regular expression of viable phone numbers. This is location independent.\r\n//  Checks we have at least three leading digits, and only valid punctuation,\r\n//  alpha characters and digits in the phone number. Does not include extension\r\n//  data. The symbol 'x' is allowed here as valid punctuation since it is often\r\n//  used as a placeholder for carrier codes, for example in Brazilian phone\r\n//  numbers. We also allow multiple '+' characters at the start.\r\n//\r\n//  Corresponds to the following:\r\n//  [digits]{minLengthNsn}|\r\n//  plus_sign*\r\n//  (([punctuation]|[star])*[digits]){3,}([punctuation]|[star]|[digits]|[alpha])*\r\n//\r\n//  The first reg-ex is to allow short numbers (two digits long) to be parsed if\r\n//  they are entered as \"15\" etc, but only if there is no punctuation in them.\r\n//  The second expression restricts the number of digits to three or more, but\r\n//  then allows them to be in international form, and to have alpha-characters\r\n//  and punctuation. We split up the two reg-exes here and combine them when\r\n//  creating the reg-ex VALID_PHONE_NUMBER_PATTERN itself so we can prefix it\r\n//  with ^ and append $ to each branch.\r\n//\r\n//  \"Note VALID_PUNCTUATION starts with a -,\r\n//   so must be the first in the range\" (c) Google devs.\r\n//  (wtf did they mean by saying that; probably nothing)\r\n//\r\nconst MIN_LENGTH_PHONE_NUMBER_PATTERN = '[' + VALID_DIGITS + ']{' + MIN_LENGTH_FOR_NSN + '}'\r\n//\r\n// And this is the second reg-exp:\r\n// (see MIN_LENGTH_PHONE_NUMBER_PATTERN for a full description of this reg-exp)\r\n//\r\nexport const VALID_PHONE_NUMBER =\r\n\t'[' + PLUS_CHARS + ']{0,1}' +\r\n\t'(?:' +\r\n\t\t'[' + VALID_PUNCTUATION + ']*' +\r\n\t\t'[' + VALID_DIGITS + ']' +\r\n\t'){3,}' +\r\n\t'[' +\r\n\t\tVALID_PUNCTUATION +\r\n\t\tVALID_DIGITS +\r\n\t']*'\r\n\r\n// This regular expression isn't present in Google's `libphonenumber`\r\n// and is only used to determine whether the phone number being input\r\n// is too short for it to even consider it a \"valid\" number.\r\n// This is just a way to differentiate between a really invalid phone\r\n// number like \"abcde\" and a valid phone number that a user has just\r\n// started inputting, like \"+1\" or \"1\": both these cases would be\r\n// considered `NOT_A_NUMBER` by Google's `libphonenumber`, but this\r\n// library can provide a more detailed error message — whether it's\r\n// really \"not a number\", or is it just a start of a valid phone number.\r\nconst VALID_PHONE_NUMBER_START_REG_EXP = new RegExp(\r\n\t'^' +\r\n\t'[' + PLUS_CHARS + ']{0,1}' +\r\n\t'(?:' +\r\n\t\t'[' + VALID_PUNCTUATION + ']*' +\r\n\t\t'[' + VALID_DIGITS + ']' +\r\n\t'){1,2}' +\r\n\t'$'\r\n, 'i')\r\n\r\nexport const VALID_PHONE_NUMBER_WITH_EXTENSION =\r\n\tVALID_PHONE_NUMBER +\r\n\t// Phone number extensions\r\n\t'(?:' + createExtensionPattern() + ')?'\r\n\r\n// The combined regular expression for valid phone numbers:\r\n//\r\nconst VALID_PHONE_NUMBER_PATTERN = new RegExp(\r\n\t// Either a short two-digit-only phone number\r\n\t'^' +\r\n\t\tMIN_LENGTH_PHONE_NUMBER_PATTERN +\r\n\t'$' +\r\n\t'|' +\r\n\t// Or a longer fully parsed phone number (min 3 characters)\r\n\t'^' +\r\n\t\tVALID_PHONE_NUMBER_WITH_EXTENSION +\r\n\t'$'\r\n, 'i')\r\n\r\n// Checks to see if the string of characters could possibly be a phone number at\r\n// all. At the moment, checks to see that the string begins with at least 2\r\n// digits, ignoring any punctuation commonly found in phone numbers. This method\r\n// does not require the number to be normalized in advance - but does assume\r\n// that leading non-number symbols have been removed, such as by the method\r\n// `extract_possible_number`.\r\n//\r\nexport default function isViablePhoneNumber(number) {\r\n\treturn number.length >= MIN_LENGTH_FOR_NSN &&\r\n\t\tVALID_PHONE_NUMBER_PATTERN.test(number)\r\n}\r\n\r\n// This is just a way to differentiate between a really invalid phone\r\n// number like \"abcde\" and a valid phone number that a user has just\r\n// started inputting, like \"+1\" or \"1\": both these cases would be\r\n// considered `NOT_A_NUMBER` by Google's `libphonenumber`, but this\r\n// library can provide a more detailed error message — whether it's\r\n// really \"not a number\", or is it just a start of a valid phone number.\r\nexport function isViablePhoneNumberStart(number) {\r\n\treturn VALID_PHONE_NUMBER_START_REG_EXP.test(number)\r\n}"], "mappings": "AAAA,SACCA,kBADD,EAECC,YAFD,EAGCC,iBAHD,EAICC,UAJD,QAKO,iBALP;AAOA,OAAOC,sBAAP,MAAmC,uCAAnC,C,CAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAMC,+BAA+B,GAAG,MAAMJ,YAAN,GAAqB,IAArB,GAA4BD,kBAA5B,GAAiD,GAAzF,C,CACA;AACA;AACA;AACA;;AACA,OAAO,IAAMM,kBAAkB,GAC9B,MAAMH,UAAN,GAAmB,QAAnB,GACA,KADA,GAEC,GAFD,GAEOD,iBAFP,GAE2B,IAF3B,GAGC,GAHD,GAGOD,YAHP,GAGsB,GAHtB,GAIA,OAJA,GAKA,GALA,GAMCC,iBAND,GAOCD,YAPD,GAQA,IATM,C,CAWP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,IAAMM,gCAAgC,GAAG,IAAIC,MAAJ,CACxC,MACA,GADA,GACML,UADN,GACmB,QADnB,GAEA,KAFA,GAGC,GAHD,GAGOD,iBAHP,GAG2B,IAH3B,GAIC,GAJD,GAIOD,YAJP,GAIsB,GAJtB,GAKA,QALA,GAMA,GAPwC,EAQvC,GARuC,CAAzC;AAUA,OAAO,IAAMQ,iCAAiC,GAC7CH,kBAAkB,GAClB;AACA,KAFA,GAEQF,sBAAsB,EAF9B,GAEmC,IAH7B,C,CAKP;AACA;;AACA,IAAMM,0BAA0B,GAAG,IAAIF,MAAJ,EAClC;AACA,MACCH,+BADD,GAEA,GAFA,GAGA,GAHA,GAIA;AACA,GALA,GAMCI,iCAND,GAOA,GATkC,EAUjC,GAViC,CAAnC,C,CAYA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,eAAe,SAASE,mBAAT,CAA6BC,MAA7B,EAAqC;EACnD,OAAOA,MAAM,CAACC,MAAP,IAAiBb,kBAAjB,IACNU,0BAA0B,CAACI,IAA3B,CAAgCF,MAAhC,CADD;AAEA,C,CAED;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASG,wBAAT,CAAkCH,MAAlC,EAA0C;EAChD,OAAOL,gCAAgC,CAACO,IAAjC,CAAsCF,MAAtC,CAAP;AACA"}