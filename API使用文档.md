# Global Timezone Spoofer API 使用文档

## 概述

本文档详细介绍了 Global Timezone Spoofer 插件的所有 API 接口，包括用户认证、激活码验证、系统配置等功能。

## 基础信息

- **API 基础URL**: `http://*************/api`
- **请求方式**: POST（除特殊说明外）
- **内容类型**: `application/json`
- **字符编码**: UTF-8

## 通用响应格式

所有 API 接口都遵循统一的响应格式：

```json
{
    "success": true|false,
    "message": "响应消息",
    "data": {}, // 响应数据（可选）
    "timestamp": 1234567890 // Unix时间戳
}
```

## 1. 用户注册 API

### 接口信息
- **URL**: `/register.php`
- **方法**: POST
- **功能**: 注册新用户账号

### 请求参数

```json
{
    "username": "用户名",
    "password": "密码"
}
```

### 参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| username | string | 是 | 用户名，3-20个字符，仅限字母数字下划线 |
| password | string | 是 | 密码，至少6个字符 |

### 响应示例

**成功响应**:
```json
{
    "success": true,
    "message": "注册成功",
    "data": {
        "user_id": 123,
        "username": "testuser",
        "expires_at": "2024-01-15 23:59:59",
        "remaining_days": 7,
        "status": "active"
    }
}
```

**失败响应**:
```json
{
    "success": false,
    "message": "用户名已存在"
}
```

### 注意事项
- 试用天数由系统配置 `default_trial_days` 决定
- 当 `default_trial_days` 为 0 时，新用户直接为过期状态
- 用户名不能重复

## 2. 用户登录 API

### 接口信息
- **URL**: `/login.php`
- **方法**: POST
- **功能**: 用户登录验证

### 请求参数

```json
{
    "username": "用户名",
    "password": "密码"
}
```

### 响应示例

**成功响应（活跃用户）**:
```json
{
    "success": true,
    "message": "登录成功",
    "data": {
        "user_id": 123,
        "username": "testuser",
        "expires_at": "2024-01-15 23:59:59",
        "remaining_days": 5,
        "status": "active",
        "purchase_link": "https://example.com/buy"
    }
}
```

**过期用户响应**:
```json
{
    "success": false,
    "message": "账户已过期",
    "data": {
        "expired": true,
        "user_data": {
            "user_id": 123,
            "username": "testuser",
            "remaining_days": 0,
            "status": "expired"
        },
        "purchase_link": "https://example.com/buy"
    }
}
```

## 3. 用户状态查询 API

### 接口信息
- **URL**: `/user_status.php`
- **方法**: GET
- **功能**: 查询用户当前状态

### 请求参数

通过 URL 参数传递：
```
GET /user_status.php?user_id=123
```

### 响应示例

```json
{
    "success": true,
    "message": "查询成功",
    "data": {
        "user_id": 123,
        "username": "testuser",
        "expires_at": "2024-01-15 23:59:59",
        "remaining_days": 5,
        "status": "active",
        "purchase_link": "https://example.com/buy"
    }
}
```

## 4. 激活码验证 API

### 接口信息
- **URL**: `/activate.php`
- **方法**: POST
- **功能**: 使用激活码延长账户有效期

### 请求参数

```json
{
    "user_id": 123,
    "activation_code": "激活码"
}
```

### 激活码验证规则

激活码必须满足以下条件：
1. 存在于激活码数据库（jihuoma.activation_codes）
2. `remark` 字段为 `'timezone'`
3. `paid` 字段为 `1`
4. `is_used` 字段为 `0`（未使用）
5. 用户未曾使用过该激活码

### 响应示例

**成功响应**:
```json
{
    "success": true,
    "message": "激活成功",
    "data": {
        "user_id": 123,
        "username": "testuser",
        "days_added": 30,
        "expires_at": "2024-02-15 23:59:59",
        "remaining_days": 35,
        "status": "active"
    }
}
```

**失败响应**:
```json
{
    "success": false,
    "message": "您已经使用过此激活码或激活码不存在"
}
```

### 激活流程说明

1. **验证用户**: 检查用户是否存在
2. **检查重复使用**: 验证用户是否已使用过该激活码
3. **验证激活码**: 在激活码数据库中验证激活码有效性
4. **计算到期时间**: 在当前到期时间基础上增加天数
5. **更新用户信息**: 更新到期时间和已使用激活码列表
6. **标记激活码**: 将激活码标记为已使用
7. **记录历史**: 在激活历史表中记录本次激活

## 5. 系统配置查询 API

### 接口信息
- **URL**: `/get_config.php`
- **方法**: GET
- **功能**: 获取系统配置项

### 请求参数

通过 URL 参数传递：
```
GET /get_config.php?key=config_key
```

### 常用配置项

| 配置键 | 说明 | 默认值 |
|--------|------|--------|
| default_trial_days | 默认试用天数 | 7 |
| usage_tutorial | 使用教程链接 | - |
| purchase_link | 购买链接 | - |

### 响应示例

```json
{
    "success": true,
    "message": "获取配置成功",
    "data": {
        "config_key": "default_trial_days",
        "config_value": "7",
        "description": "默认试用天数"
    }
}
```

## 6. 服务信息查询 API

### 接口信息
- **URL**: `/get_service_info.php`
- **方法**: GET
- **功能**: 获取服务相关信息（购买链接等）

### 响应示例

```json
{
    "success": true,
    "message": "获取服务信息成功",
    "data": {
        "purchase_link": "https://example.com/buy",
        "service_status": "active",
        "announcement": "系统公告内容"
    }
}
```

## 7. 错误码说明

### HTTP 状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 402 | 账户已过期 |
| 404 | 资源不存在 |
| 405 | 请求方法不允许 |
| 409 | 资源冲突（如用户名已存在） |
| 500 | 服务器内部错误 |

### 业务错误消息

| 错误消息 | 说明 | 解决方案 |
|----------|------|----------|
| 用户名和密码不能为空 | 必填参数缺失 | 检查请求参数 |
| 用户名长度必须在3-20个字符之间 | 用户名格式错误 | 调整用户名长度 |
| 用户名只能包含字母、数字和下划线 | 用户名包含非法字符 | 使用合法字符 |
| 密码长度至少6个字符 | 密码太短 | 增加密码长度 |
| 用户名已存在 | 注册时用户名重复 | 更换用户名 |
| 用户名或密码错误 | 登录凭据错误 | 检查用户名密码 |
| 账户已过期 | 用户试用期结束 | 使用激活码延期 |
| 您已经使用过此激活码或激活码不存在 | 激活码无效 | 检查激活码有效性 |
| 数据库连接失败 | 服务器问题 | 联系技术支持 |

## 8. 数据库结构说明

### 用户表 (users)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | int | 用户ID（主键） |
| username | varchar(50) | 用户名（唯一） |
| password_hash | varchar(255) | 密码哈希 |
| expires_at | datetime | 到期时间 |
| status | enum | 状态：active/expired |
| used_activation_codes | text | 已使用的激活码列表 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

### 激活码表 (activation_codes)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | int | 激活码ID（主键） |
| code | varchar(32) | 激活码（唯一） |
| is_used | tinyint(1) | 是否已使用 |
| use_times | int | 增加天数 |
| paid | int | 是否已付费 |
| remark | varchar(255) | 备注（timezone） |
| created_at | timestamp | 创建时间 |
| used_at | timestamp | 使用时间 |

### 系统配置表 (system_config)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | int | 配置ID（主键） |
| config_key | varchar(100) | 配置键（唯一） |
| config_value | text | 配置值 |
| description | varchar(255) | 配置描述 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

## 9. 使用示例

### JavaScript 示例

```javascript
// API 基础配置
const API_BASE = 'http://*************/api';

// 通用请求函数
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    };

    const response = await fetch(url, { ...defaultOptions, ...options });
    const result = await response.json();

    if (!response.ok && response.status !== 402) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return result;
}

// 用户注册
async function registerUser(username, password) {
    try {
        const response = await apiRequest(`${API_BASE}/register.php`, {
            method: 'POST',
            body: JSON.stringify({
                username: username,
                password: password
            })
        });

        if (response.success) {
            console.log('注册成功:', response.data);
            return response.data;
        } else {
            console.error('注册失败:', response.message);
            return null;
        }
    } catch (error) {
        console.error('注册请求失败:', error);
        return null;
    }
}

// 用户登录
async function loginUser(username, password) {
    try {
        const response = await apiRequest(`${API_BASE}/login.php`, {
            method: 'POST',
            body: JSON.stringify({
                username: username,
                password: password
            })
        });

        if (response.success) {
            console.log('登录成功:', response.data);
            return response.data;
        } else {
            console.error('登录失败:', response.message);
            // 检查是否为过期用户
            if (response.data && response.data.expired) {
                console.log('用户已过期，显示激活界面');
                return { expired: true, userData: response.data.user_data };
            }
            return null;
        }
    } catch (error) {
        console.error('登录请求失败:', error);
        return null;
    }
}

// 激活码验证
async function activateCode(userId, activationCode) {
    try {
        const response = await apiRequest(`${API_BASE}/activate.php`, {
            method: 'POST',
            body: JSON.stringify({
                user_id: userId,
                activation_code: activationCode
            })
        });

        if (response.success) {
            console.log('激活成功:', response.data);
            return response.data;
        } else {
            console.error('激活失败:', response.message);
            return null;
        }
    } catch (error) {
        console.error('激活请求失败:', error);
        return null;
    }
}

// 查询用户状态
async function getUserStatus(userId) {
    try {
        const response = await apiRequest(`${API_BASE}/user_status.php?user_id=${userId}`);

        if (response.success) {
            console.log('用户状态:', response.data);
            return response.data;
        } else {
            console.error('查询失败:', response.message);
            return null;
        }
    } catch (error) {
        console.error('状态查询失败:', error);
        return null;
    }
}

// 获取系统配置
async function getConfig(configKey) {
    try {
        const response = await apiRequest(`${API_BASE}/get_config.php?key=${configKey}`);

        if (response.success) {
            console.log('配置信息:', response.data);
            return response.data.config_value;
        } else {
            console.error('获取配置失败:', response.message);
            return null;
        }
    } catch (error) {
        console.error('配置请求失败:', error);
        return null;
    }
}
```

## 10. 安全注意事项

### 密码安全
- 前端不应存储明文密码
- 使用 HTTPS 传输敏感信息
- 密码在服务端使用安全哈希算法存储

### 激活码安全
- 激活码一次性使用，使用后立即标记
- 防止激活码重复使用
- 记录激活历史便于审计

### 数据验证
- 所有输入参数都进行严格验证
- 防止 SQL 注入攻击
- 限制请求频率防止暴力破解

## 11. 常见问题

### Q: 如何处理用户过期？
A: 当用户登录时返回过期状态，前端应显示激活码输入界面，引导用户购买和使用激活码。

### Q: 激活码可以重复使用吗？
A: 不可以。每个激活码只能使用一次，使用后会被标记为已使用状态。

### Q: 如何设置试用天数？
A: 在 system_config 表中修改 default_trial_days 配置项，设置为 0 表示无试用期。

### Q: 用户数据如何存储？
A: 用户数据存储在浏览器的 chrome.storage.sync 中，支持跨设备同步。

## 12. 更新日志

### v2.9.7
- 添加使用教程按钮功能
- 优化激活码验证逻辑
- 修复试用天数为0时的问题
- 移除激活码位数限制

### v2.9.6
- 实现真实激活码数据库验证
- 添加激活历史记录
- 优化错误处理机制

---

**技术支持**: 如有问题请联系开发团队
**最后更新**: 2024年1月
