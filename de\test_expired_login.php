<?php
// 测试过期用户登录
header('Content-Type: text/html; charset=utf-8');
require_once 'config.php';

$result = null;
$error = null;

// 获取一个过期用户进行测试
$pdo = getDBConnection();
$expiredUser = null;
if ($pdo) {
    $stmt = $pdo->prepare("SELECT username FROM users WHERE status = 'expired' LIMIT 1");
    $stmt->execute();
    $expiredUser = $stmt->fetch();
}

if ($_POST && isset($_POST['username']) && isset($_POST['password'])) {
    $username = trim($_POST['username']);
    $password = trim($_POST['password']);
    
    try {
        // 模拟登录API调用
        $postData = json_encode([
            'username' => $username,
            'password' => $password
        ]);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://103.96.75.196/api/login.php');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($postData)
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response === false) {
            throw new Exception('请求失败');
        }
        
        $result = [
            'http_code' => $httpCode,
            'response' => json_decode($response, true),
            'raw_response' => $response
        ];
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>过期用户登录测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        .json {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .info {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .expired-user {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 过期用户登录测试</h1>
        
        <div class="info">
            <h3>📋 测试说明</h3>
            <p>此工具专门测试数据库中status为'expired'的用户登录时的API响应。</p>
            <p>正确的行为应该是：返回HTTP 402状态码，包含expired=true和user_data字段。</p>
        </div>
        
        <?php if ($expiredUser): ?>
            <div class="expired-user">
                <h3>🔍 发现过期用户</h3>
                <p><strong>用户名：</strong><?php echo htmlspecialchars($expiredUser['username']); ?></p>
                <p>您可以使用此用户名进行测试（需要知道密码）</p>
            </div>
        <?php else: ?>
            <div class="expired-user">
                <h3>⚠️ 没有过期用户</h3>
                <p>数据库中没有找到status为'expired'的用户。</p>
                <p>您可以在用户状态测试工具中将一个用户设置为过期状态。</p>
            </div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="username">用户名：</label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    value="<?php echo htmlspecialchars($_POST['username'] ?? ($expiredUser['username'] ?? '')); ?>"
                    placeholder="输入用户名"
                    required
                >
            </div>
            
            <div class="form-group">
                <label for="password">密码：</label>
                <input 
                    type="password" 
                    id="password" 
                    name="password" 
                    placeholder="输入密码"
                    required
                >
            </div>
            
            <button type="submit" class="btn">🚀 测试过期用户登录</button>
        </form>
        
        <?php if ($error): ?>
            <div class="result error">
                <h3>❌ 请求错误</h3>
                <p><?php echo htmlspecialchars($error); ?></p>
            </div>
        <?php endif; ?>
        
        <?php if ($result): ?>
            <div class="result <?php echo $result['response']['success'] ? 'success' : 'error'; ?>">
                <h3><?php echo $result['response']['success'] ? '✅ 登录成功' : '❌ 登录失败'; ?></h3>
                
                <p><strong>HTTP状态码：</strong> <?php echo $result['http_code']; ?></p>
                <p><strong>响应消息：</strong> <?php echo htmlspecialchars($result['response']['message'] ?? '无消息'); ?></p>
                
                <?php if ($result['http_code'] == 402): ?>
                    <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <h4>✅ 正确！这是过期用户的预期响应</h4>
                        <p>HTTP 402 状态码表示需要付费，这是过期用户的正确响应。</p>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($result['response']['data'])): ?>
                    <h4>📊 响应数据：</h4>
                    <div class="json"><?php echo json_encode($result['response']['data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?></div>
                    
                    <?php if (isset($result['response']['data']['expired']) && $result['response']['data']['expired']): ?>
                        <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;">
                            <h4>✅ 包含过期标识</h4>
                            <p>响应数据包含expired=true，前端应该能识别并显示过期界面。</p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (isset($result['response']['data']['user_data'])): ?>
                        <div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0;">
                            <h4>✅ 包含用户数据</h4>
                            <p>响应数据包含user_data字段，前端可以显示用户信息。</p>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
                
                <h4>🔍 完整响应：</h4>
                <div class="json"><?php echo htmlspecialchars($result['raw_response']); ?></div>
            </div>
        <?php endif; ?>
        
        <div style="margin-top: 30px; padding: 20px; background: #fff3cd; border-radius: 8px;">
            <h3>💡 预期结果</h3>
            <ul>
                <li><strong>HTTP状态码：</strong> 402 (Payment Required)</li>
                <li><strong>success：</strong> false</li>
                <li><strong>message：</strong> "试用期已结束，请购买激活码继续使用"</li>
                <li><strong>data.expired：</strong> true</li>
                <li><strong>data.user_data：</strong> 包含用户信息的对象</li>
                <li><strong>data.purchase_link：</strong> 购买链接</li>
            </ul>
        </div>
    </div>
</body>
</html>
