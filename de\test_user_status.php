<?php
// 用户状态测试工具
header('Content-Type: text/html; charset=utf-8');
require_once 'config.php';

$pdo = getDBConnection();
if (!$pdo) {
    die('数据库连接失败');
}

// 获取所有用户信息
$stmt = $pdo->prepare("
    SELECT
        id, username, status,
        remaining_days, trial_days,
        created_at, last_login_at,
        DATEDIFF(NOW(), created_at) as days_since_registration
    FROM users
    ORDER BY created_at DESC
");
$stmt->execute();
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 处理状态更新请求
if ($_POST && isset($_POST['user_id']) && isset($_POST['action'])) {
    $userId = $_POST['user_id'];
    $action = $_POST['action'];
    
    try {
        if ($action === 'reset_status') {
            // 重置用户状态为active
            $stmt = $pdo->prepare("UPDATE users SET status = 'active' WHERE id = ?");
            $stmt->execute([$userId]);
            $message = "用户状态已重置为active";
        } elseif ($action === 'set_expired') {
            // 设置用户状态为expired
            $stmt = $pdo->prepare("UPDATE users SET status = 'expired' WHERE id = ?");
            $stmt->execute([$userId]);
            $message = "用户状态已设置为expired";
        } elseif ($action === 'add_days') {
            // 添加30天试用
            $stmt = $pdo->prepare("UPDATE users SET remaining_days = remaining_days + 30 WHERE id = ?");
            $stmt->execute([$userId]);
            $message = "已为用户添加30天";
        }
        
        // 刷新页面显示更新后的数据
        header('Location: ' . $_SERVER['PHP_SELF'] . '?msg=' . urlencode($message));
        exit;
    } catch (Exception $e) {
        $error = "操作失败：" . $e->getMessage();
    }
}

$message = $_GET['msg'] ?? '';
$error = $error ?? '';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户状态测试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.active {
            background: #d4edda;
            color: #155724;
        }
        .status.expired {
            background: #f8d7da;
            color: #721c24;
        }
        .status.suspended {
            background: #fff3cd;
            color: #856404;
        }
        .btn {
            padding: 6px 12px;
            margin: 2px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .remaining-days {
            font-weight: bold;
        }
        .remaining-days.expired {
            color: #dc3545;
        }
        .remaining-days.active {
            color: #28a745;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #495057;
        }
        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 用户状态测试工具</h1>
        
        <?php if ($message): ?>
            <div class="message success"><?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="message error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <?php
        // 统计信息
        $totalUsers = count($users);
        $activeUsers = count(array_filter($users, fn($u) => $u['status'] === 'active'));
        $expiredUsers = count(array_filter($users, fn($u) => $u['status'] === 'expired'));
        $suspendedUsers = count(array_filter($users, fn($u) => $u['status'] === 'suspended'));
        ?>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number"><?php echo $totalUsers; ?></div>
                <div class="stat-label">总用户数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $activeUsers; ?></div>
                <div class="stat-label">活跃用户</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $expiredUsers; ?></div>
                <div class="stat-label">过期用户</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $suspendedUsers; ?></div>
                <div class="stat-label">暂停用户</div>
            </div>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>用户名</th>
                    <th>状态</th>
                    <th>剩余天数</th>
                    <th>试用天数</th>
                    <th>注册天数</th>
                    <th>注册时间</th>
                    <th>最后登录</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($users as $user): ?>
                    <?php
                    // 计算实际剩余天数
                    $actualRemainingDays = updateUserRemainingDays($pdo, $user['id']);
                    ?>
                    <tr>
                        <td><?php echo $user['id']; ?></td>
                        <td><?php echo htmlspecialchars($user['username']); ?></td>
                        <td>
                            <span class="status <?php echo $user['status']; ?>">
                                <?php echo $user['status']; ?>
                            </span>
                        </td>
                        <td>
                            <span class="remaining-days <?php echo $actualRemainingDays > 0 ? 'active' : 'expired'; ?>">
                                <?php echo $actualRemainingDays; ?>天
                            </span>
                        </td>
                        <td><?php echo $user['trial_days']; ?>天</td>
                        <td><?php echo $user['days_since_registration']; ?>天</td>
                        <td><?php echo date('Y-m-d H:i', strtotime($user['created_at'])); ?></td>
                        <td><?php echo $user['last_login_at'] ? date('Y-m-d H:i', strtotime($user['last_login_at'])) : '从未登录'; ?></td>
                        <td>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                <button type="submit" name="action" value="reset_status" class="btn btn-primary">重置状态</button>
                                <button type="submit" name="action" value="set_expired" class="btn btn-warning">设为过期</button>
                                <button type="submit" name="action" value="add_days" class="btn btn-success">+30天</button>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <div style="margin-top: 30px; padding: 20px; background: #e3f2fd; border-radius: 8px;">
            <h3>🧪 测试说明</h3>
            <ul>
                <li><strong>重置状态</strong>：将用户状态设置为 'active'</li>
                <li><strong>设为过期</strong>：将用户状态设置为 'expired'</li>
                <li><strong>+30天</strong>：为用户添加30天使用时间</li>
                <li><strong>剩余天数</strong>：基于注册时间和试用天数的实时计算</li>
                <li><strong>状态说明</strong>：active=正常，expired=过期，suspended=暂停</li>
            </ul>
        </div>
    </div>
</body>
</html>
