{"version": 3, "file": "extractCountryCallingCode.test.js", "names": ["extractCountryCallingCode", "metadata", "type", "describe", "it", "should", "deep", "equal", "countryCallingCodeSource", "countryCallingCode", "number"], "sources": ["../../source/helpers/extractCountryCallingCode.test.js"], "sourcesContent": ["import extractCountryCallingCode from './extractCountryCallingCode.js'\r\nimport metadata from '../../metadata.min.json' assert { type: 'json' }\r\n\r\ndescribe('extractCountryCallingCode', () => {\r\n\tit('should extract country calling code from a number', () => {\r\n\t\textractCountryCallingCode('+78005553535', null, null, metadata).should.deep.equal({\r\n\t\t\tcountryCallingCodeSource: 'FROM_NUMBER_WITH_PLUS_SIGN',\r\n\t\t\tcountryCallingCode: '7',\r\n\t\t\tnumber: '8005553535'\r\n\t\t})\r\n\r\n\t\textractCountryCallingCode('+7800', null, null, metadata).should.deep.equal({\r\n\t\t\tcountryCallingCodeSource: 'FROM_NUMBER_WITH_PLUS_SIGN',\r\n\t\t\tcountryCallingCode: '7',\r\n\t\t\tnumber: '800'\r\n\t\t})\r\n\r\n\t\textractCountryCallingCode('', null, null, metadata).should.deep.equal({})\r\n\t})\r\n})\r\n"], "mappings": "AAAA,OAAOA,yBAAP,MAAsC,gCAAtC;AACA,OAAOC,QAAP,MAAqB,yBAArB,UAAwDC,IAAI,EAAE,MAA9D;AAEAC,QAAQ,CAAC,2BAAD,EAA8B,YAAM;EAC3CC,EAAE,CAAC,mDAAD,EAAsD,YAAM;IAC7DJ,yBAAyB,CAAC,cAAD,EAAiB,IAAjB,EAAuB,IAAvB,EAA6BC,QAA7B,CAAzB,CAAgEI,MAAhE,CAAuEC,IAAvE,CAA4EC,KAA5E,CAAkF;MACjFC,wBAAwB,EAAE,4BADuD;MAEjFC,kBAAkB,EAAE,GAF6D;MAGjFC,MAAM,EAAE;IAHyE,CAAlF;IAMAV,yBAAyB,CAAC,OAAD,EAAU,IAAV,EAAgB,IAAhB,EAAsBC,QAAtB,CAAzB,CAAyDI,MAAzD,CAAgEC,IAAhE,CAAqEC,KAArE,CAA2E;MAC1EC,wBAAwB,EAAE,4BADgD;MAE1EC,kBAAkB,EAAE,GAFsD;MAG1EC,MAAM,EAAE;IAHkE,CAA3E;IAMAV,yBAAyB,CAAC,EAAD,EAAK,IAAL,EAAW,IAAX,EAAiBC,QAAjB,CAAzB,CAAoDI,MAApD,CAA2DC,IAA3D,CAAgEC,KAAhE,CAAsE,EAAtE;EACA,CAdC,CAAF;AAeA,CAhBO,CAAR"}