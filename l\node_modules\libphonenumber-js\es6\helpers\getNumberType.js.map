{"version": 3, "file": "getNumberType.js", "names": ["<PERSON><PERSON><PERSON>", "matchesEntirely", "NON_FIXED_LINE_PHONE_TYPES", "getNumberType", "input", "options", "metadata", "country", "countryCallingCode", "selectNumberingPlan", "nationalNumber", "v2", "phone", "nationalNumberPattern", "isNumberTypeEqualTo", "type", "pattern", "possibleLengths", "indexOf", "length"], "sources": ["../../source/helpers/getNumberType.js"], "sourcesContent": ["import Metadata from '../metadata.js'\r\nimport matchesEntirely from './matchesEntirely.js'\r\n\r\nconst NON_FIXED_LINE_PHONE_TYPES = [\r\n\t'MOBILE',\r\n\t'PREMIUM_RATE',\r\n\t'TOLL_FREE',\r\n\t'SHARED_COST',\r\n\t'VOIP',\r\n\t'PERSONAL_NUMBER',\r\n\t'PAGER',\r\n\t'UAN',\r\n\t'VOICEMAIL'\r\n]\r\n\r\n// Finds out national phone number type (fixed line, mobile, etc)\r\nexport default function getNumberType(input, options, metadata)\r\n{\r\n\t// If assigning the `{}` default value is moved to the arguments above,\r\n\t// code coverage would decrease for some weird reason.\r\n\toptions = options || {}\r\n\r\n\t// When `parse()` returns an empty object — `{}` —\r\n\t// that means that the phone number is malformed,\r\n\t// so it can't possibly be valid.\r\n\tif (!input.country && !input.countryCallingCode) {\r\n\t\treturn\r\n\t}\r\n\r\n\tmetadata = new Metadata(metadata)\r\n\r\n\tmetadata.selectNumberingPlan(input.country, input.countryCallingCode)\r\n\r\n\tconst nationalNumber = options.v2 ? input.nationalNumber : input.phone\r\n\r\n\t// The following is copy-pasted from the original function:\r\n\t// https://github.com/googlei18n/libphonenumber/blob/3ea547d4fbaa2d0b67588904dfa5d3f2557c27ff/javascript/i18n/phonenumbers/phonenumberutil.js#L2835\r\n\r\n\t// Is this national number even valid for this country\r\n\tif (!matchesEntirely(nationalNumber, metadata.nationalNumberPattern())) {\r\n\t\treturn\r\n\t}\r\n\r\n\t// Is it fixed line number\r\n\tif (isNumberTypeEqualTo(nationalNumber, 'FIXED_LINE', metadata)) {\r\n\t\t// Because duplicate regular expressions are removed\r\n\t\t// to reduce metadata size, if \"mobile\" pattern is \"\"\r\n\t\t// then it means it was removed due to being a duplicate of the fixed-line pattern.\r\n\t\t//\r\n\t\tif (metadata.type('MOBILE') && metadata.type('MOBILE').pattern() === '') {\r\n\t\t\treturn 'FIXED_LINE_OR_MOBILE'\r\n\t\t}\r\n\r\n\t\t// `MOBILE` type pattern isn't included if it matched `FIXED_LINE` one.\r\n\t\t// For example, for \"US\" country.\r\n\t\t// Old metadata (< `1.0.18`) had a specific \"types\" data structure\r\n\t\t// that happened to be `undefined` for `MOBILE` in that case.\r\n\t\t// Newer metadata (>= `1.0.18`) has another data structure that is\r\n\t\t// not `undefined` for `MOBILE` in that case (it's just an empty array).\r\n\t\t// So this `if` is just for backwards compatibility with old metadata.\r\n\t\tif (!metadata.type('MOBILE')) {\r\n\t\t\treturn 'FIXED_LINE_OR_MOBILE'\r\n\t\t}\r\n\r\n\t\t// Check if the number happens to qualify as both fixed line and mobile.\r\n\t\t// (no such country in the minimal metadata set)\r\n\t\t/* istanbul ignore if */\r\n\t\tif (isNumberTypeEqualTo(nationalNumber, 'MOBILE', metadata)) {\r\n\t\t\treturn 'FIXED_LINE_OR_MOBILE'\r\n\t\t}\r\n\r\n\t\treturn 'FIXED_LINE'\r\n\t}\r\n\r\n\tfor (const type of NON_FIXED_LINE_PHONE_TYPES) {\r\n\t\tif (isNumberTypeEqualTo(nationalNumber, type, metadata)) {\r\n\t\t\treturn type\r\n\t\t}\r\n\t}\r\n}\r\n\r\nexport function isNumberTypeEqualTo(nationalNumber, type, metadata) {\r\n\ttype = metadata.type(type)\r\n\tif (!type || !type.pattern()) {\r\n\t\treturn false\r\n\t}\r\n\t// Check if any possible number lengths are present;\r\n\t// if so, we use them to avoid checking\r\n\t// the validation pattern if they don't match.\r\n\t// If they are absent, this means they match\r\n\t// the general description, which we have\r\n\t// already checked before a specific number type.\r\n\tif (type.possibleLengths() &&\r\n\t\ttype.possibleLengths().indexOf(nationalNumber.length) < 0) {\r\n\t\treturn false\r\n\t}\r\n\treturn matchesEntirely(nationalNumber, type.pattern())\r\n}"], "mappings": ";;;;;;AAAA,OAAOA,QAAP,MAAqB,gBAArB;AACA,OAAOC,eAAP,MAA4B,sBAA5B;AAEA,IAAMC,0BAA0B,GAAG,CAClC,QADkC,EAElC,cAFkC,EAGlC,WAHkC,EAIlC,aAJkC,EAKlC,MALkC,EAMlC,iBANkC,EAOlC,OAPkC,EAQlC,KARkC,EASlC,WATkC,CAAnC,C,CAYA;;AACA,eAAe,SAASC,aAAT,CAAuBC,KAAvB,EAA8BC,OAA9B,EAAuCC,QAAvC,EACf;EACC;EACA;EACAD,OAAO,GAAGA,OAAO,IAAI,EAArB,CAHD,CAKC;EACA;EACA;;EACA,IAAI,CAACD,KAAK,CAACG,OAAP,IAAkB,CAACH,KAAK,CAACI,kBAA7B,EAAiD;IAChD;EACA;;EAEDF,QAAQ,GAAG,IAAIN,QAAJ,CAAaM,QAAb,CAAX;EAEAA,QAAQ,CAACG,mBAAT,CAA6BL,KAAK,CAACG,OAAnC,EAA4CH,KAAK,CAACI,kBAAlD;EAEA,IAAME,cAAc,GAAGL,OAAO,CAACM,EAAR,GAAaP,KAAK,CAACM,cAAnB,GAAoCN,KAAK,CAACQ,KAAjE,CAhBD,CAkBC;EACA;EAEA;;EACA,IAAI,CAACX,eAAe,CAACS,cAAD,EAAiBJ,QAAQ,CAACO,qBAAT,EAAjB,CAApB,EAAwE;IACvE;EACA,CAxBF,CA0BC;;;EACA,IAAIC,mBAAmB,CAACJ,cAAD,EAAiB,YAAjB,EAA+BJ,QAA/B,CAAvB,EAAiE;IAChE;IACA;IACA;IACA;IACA,IAAIA,QAAQ,CAACS,IAAT,CAAc,QAAd,KAA2BT,QAAQ,CAACS,IAAT,CAAc,QAAd,EAAwBC,OAAxB,OAAsC,EAArE,EAAyE;MACxE,OAAO,sBAAP;IACA,CAP+D,CAShE;IACA;IACA;IACA;IACA;IACA;IACA;;;IACA,IAAI,CAACV,QAAQ,CAACS,IAAT,CAAc,QAAd,CAAL,EAA8B;MAC7B,OAAO,sBAAP;IACA,CAlB+D,CAoBhE;IACA;;IACA;;;IACA,IAAID,mBAAmB,CAACJ,cAAD,EAAiB,QAAjB,EAA2BJ,QAA3B,CAAvB,EAA6D;MAC5D,OAAO,sBAAP;IACA;;IAED,OAAO,YAAP;EACA;;EAED,qDAAmBJ,0BAAnB,wCAA+C;IAAA,IAApCa,IAAoC;;IAC9C,IAAID,mBAAmB,CAACJ,cAAD,EAAiBK,IAAjB,EAAuBT,QAAvB,CAAvB,EAAyD;MACxD,OAAOS,IAAP;IACA;EACD;AACD;AAED,OAAO,SAASD,mBAAT,CAA6BJ,cAA7B,EAA6CK,IAA7C,EAAmDT,QAAnD,EAA6D;EACnES,IAAI,GAAGT,QAAQ,CAACS,IAAT,CAAcA,IAAd,CAAP;;EACA,IAAI,CAACA,IAAD,IAAS,CAACA,IAAI,CAACC,OAAL,EAAd,EAA8B;IAC7B,OAAO,KAAP;EACA,CAJkE,CAKnE;EACA;EACA;EACA;EACA;EACA;;;EACA,IAAID,IAAI,CAACE,eAAL,MACHF,IAAI,CAACE,eAAL,GAAuBC,OAAvB,CAA+BR,cAAc,CAACS,MAA9C,IAAwD,CADzD,EAC4D;IAC3D,OAAO,KAAP;EACA;;EACD,OAAOlB,eAAe,CAACS,cAAD,EAAiBK,IAAI,CAACC,OAAL,EAAjB,CAAtB;AACA"}