<?php
// 调试登录API
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-Custom-Header');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    require_once 'config.php';
    
    echo json_encode([
        'success' => true,
        'message' => 'config.php 加载成功',
        'functions_exist' => [
            'isUserExpired' => function_exists('isUserExpired'),
            'calculateRemainingDays' => function_exists('calculateRemainingDays'),
            'updateUserStatus' => function_exists('updateUserStatus'),
            'extendUserExpiry' => function_exists('extendUserExpiry')
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'config.php 加载失败: ' . $e->getMessage(),
        'error' => $e->getTraceAsString()
    ]);
}
?>
