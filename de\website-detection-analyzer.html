<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站检测分析器 - Registration Rejection Analyzer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .analysis-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .analysis-card h3 {
            margin-top: 0;
            color: #fff;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .result {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            margin: 8px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            border-left: 3px solid #4CAF50;
        }
        
        .warning {
            border-left-color: #ff9800;
            background: rgba(255, 152, 0, 0.1);
        }
        
        .error {
            border-left-color: #f44336;
            background: rgba(244, 67, 54, 0.1);
        }
        
        .success {
            border-left-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background: #ff9800;
        }
        
        .btn-warning:hover {
            background: #f57c00;
        }
        
        .network-monitor {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        
        .network-monitor h3 {
            margin-top: 0;
            color: #2196F3;
        }
        
        .url-input {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            margin-bottom: 10px;
        }
        
        .url-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }
        
        .detection-summary {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.3);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .detection-summary h3 {
            margin-top: 0;
            color: #f44336;
        }
        
        .auto-monitor {
            text-align: center;
            margin: 20px 0;
        }
        
        .auto-monitor label {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 网站检测分析器</h1>
        <h2>Registration Rejection Analyzer</h2>
        <p>分析网站反欺诈系统和注册拒绝原因</p>
    </div>
    
    <div class="network-monitor">
        <h3>🌐 网络请求监控</h3>
        <input type="text" class="url-input" id="target-url" placeholder="输入要分析的网站URL (例如: https://example.com)">
        <button class="btn" onclick="startNetworkMonitoring()">开始监控</button>
        <button class="btn btn-warning" onclick="clearNetworkLog()">清除日志</button>
        <div id="network-log"></div>
    </div>
    
    <div class="detection-summary" id="detection-summary" style="display: none;">
        <h3>⚠️ 检测到的潜在问题</h3>
        <div id="summary-content"></div>
    </div>
    
    <div class="auto-monitor">
        <label>
            <input type="checkbox" id="auto-analyze" checked>
            <span>自动分析网络请求</span>
        </label>
    </div>
    
    <div class="analysis-grid">
        <div class="analysis-card">
            <h3>🌍 地理位置一致性</h3>
            <button class="btn" onclick="analyzeGeolocation()">检查地理位置</button>
            <div id="geolocation-analysis"></div>
        </div>
        
        <div class="analysis-card">
            <h3>🕐 时区一致性</h3>
            <button class="btn" onclick="analyzeTimezone()">检查时区</button>
            <div id="timezone-analysis"></div>
        </div>
        
        <div class="analysis-card">
            <h3>🌐 网络指纹</h3>
            <button class="btn" onclick="analyzeNetworkFingerprint()">检查网络指纹</button>
            <div id="network-analysis"></div>
        </div>
        
        <div class="analysis-card">
            <h3>🖥️ 设备指纹</h3>
            <button class="btn" onclick="analyzeDeviceFingerprint()">检查设备指纹</button>
            <div id="device-analysis"></div>
        </div>
        
        <div class="analysis-card">
            <h3>🔒 反欺诈检测</h3>
            <button class="btn" onclick="analyzeFraudDetection()">检查反欺诈</button>
            <div id="fraud-analysis"></div>
        </div>
        
        <div class="analysis-card">
            <h3>📊 行为分析</h3>
            <button class="btn" onclick="analyzeBehavior()">检查行为模式</button>
            <div id="behavior-analysis"></div>
        </div>
    </div>
    
    <script>
        let networkRequests = [];
        let isMonitoring = false;
        
        function addResult(containerId, content, type = 'result') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = content;
            container.appendChild(div);
        }
        
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }
        
        // 网络请求监控
        function startNetworkMonitoring() {
            const targetUrl = document.getElementById('target-url').value;
            if (!targetUrl) {
                alert('请输入要监控的网站URL');
                return;
            }
            
            isMonitoring = true;
            networkRequests = [];
            clearResults('network-log');
            
            addResult('network-log', `🎯 开始监控: ${targetUrl}`, 'success');
            addResult('network-log', '📡 正在拦截网络请求...', 'result');
            
            // 监控fetch请求
            const originalFetch = window.fetch;
            window.fetch = function(input, init) {
                if (isMonitoring) {
                    const url = typeof input === 'string' ? input : input.url;
                    logNetworkRequest('FETCH', url, init);
                }
                return originalFetch.apply(this, arguments);
            };
            
            // 监控XMLHttpRequest
            const originalXHROpen = XMLHttpRequest.prototype.open;
            XMLHttpRequest.prototype.open = function(method, url) {
                if (isMonitoring) {
                    logNetworkRequest('XHR', url, { method });
                }
                return originalXHROpen.apply(this, arguments);
            };
            
            setTimeout(() => {
                if (document.getElementById('auto-analyze').checked) {
                    analyzeNetworkRequests();
                }
            }, 5000);
        }
        
        function logNetworkRequest(type, url, options) {
            const request = {
                type,
                url,
                options,
                timestamp: new Date().toISOString()
            };
            networkRequests.push(request);
            
            // 检查是否是反欺诈相关请求
            const suspiciousPatterns = [
                'verisoul', 'fraud', 'risk', 'verify', 'trust', 'security',
                'fingerprint', 'device', 'bot', 'captcha', 'recaptcha',
                'cloudflare', 'akamai', 'incapsula', 'distil', 'perimeterx'
            ];
            
            const isSuspicious = suspiciousPatterns.some(pattern => 
                url.toLowerCase().includes(pattern)
            );
            
            const resultType = isSuspicious ? 'warning' : 'result';
            addResult('network-log', 
                `${type}: ${url} ${isSuspicious ? '⚠️ 可疑' : ''}`, 
                resultType
            );
        }
        
        function clearNetworkLog() {
            isMonitoring = false;
            networkRequests = [];
            clearResults('network-log');
        }
        
        function analyzeNetworkRequests() {
            const suspiciousRequests = networkRequests.filter(req => {
                const url = req.url.toLowerCase();
                return url.includes('verisoul') || url.includes('fraud') || 
                       url.includes('risk') || url.includes('fingerprint') ||
                       url.includes('bot') || url.includes('captcha');
            });
            
            if (suspiciousRequests.length > 0) {
                document.getElementById('detection-summary').style.display = 'block';
                const summaryContent = document.getElementById('summary-content');
                summaryContent.innerHTML = `
                    <div>检测到 ${suspiciousRequests.length} 个可疑的反欺诈请求:</div>
                    ${suspiciousRequests.map(req => `<div>• ${req.url}</div>`).join('')}
                `;
            }
        }
        
        function analyzeGeolocation() {
            clearResults('geolocation-analysis');
            
            // 检查地理位置API
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;
                        
                        addResult('geolocation-analysis', `📍 当前坐标: ${lat.toFixed(4)}, ${lng.toFixed(4)}`, 'success');
                        
                        // 检查坐标是否在美国范围内
                        if (lat >= 24.396308 && lat <= 49.384358 && lng >= -125.0 && lng <= -66.93457) {
                            addResult('geolocation-analysis', '✅ 坐标在美国范围内', 'success');
                        } else {
                            addResult('geolocation-analysis', '❌ 坐标不在美国范围内', 'error');
                        }
                        
                        // 检查精度
                        if (position.coords.accuracy < 100) {
                            addResult('geolocation-analysis', `✅ 位置精度: ${position.coords.accuracy}米`, 'success');
                        } else {
                            addResult('geolocation-analysis', `⚠️ 位置精度较低: ${position.coords.accuracy}米`, 'warning');
                        }
                    },
                    (error) => {
                        addResult('geolocation-analysis', `❌ 地理位置错误: ${error.message}`, 'error');
                    }
                );
            } else {
                addResult('geolocation-analysis', '❌ 地理位置API不可用', 'error');
            }
            
            // 检查IP地理位置
            fetch('https://ipapi.co/json/')
                .then(response => response.json())
                .then(data => {
                    if (data.country_code === 'US') {
                        addResult('geolocation-analysis', `✅ IP地理位置: ${data.city}, ${data.region}, ${data.country_name}`, 'success');
                    } else {
                        addResult('geolocation-analysis', `❌ IP地理位置不在美国: ${data.country_name}`, 'error');
                    }
                })
                .catch(error => {
                    addResult('geolocation-analysis', '⚠️ 无法获取IP地理位置', 'warning');
                });
        }
        
        function analyzeTimezone() {
            clearResults('timezone-analysis');
            
            const now = new Date();
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            const offset = now.getTimezoneOffset();
            
            addResult('timezone-analysis', `🕐 检测到时区: ${timezone}`, 'result');
            addResult('timezone-analysis', `⏰ 时区偏移: ${offset} 分钟`, 'result');
            
            // 检查是否是美国时区
            const usTimezones = [
                'America/New_York', 'America/Chicago', 'America/Denver', 
                'America/Los_Angeles', 'America/Phoenix', 'America/Anchorage',
                'Pacific/Honolulu'
            ];
            
            if (usTimezones.includes(timezone)) {
                addResult('timezone-analysis', '✅ 时区设置正确 (美国时区)', 'success');
            } else {
                addResult('timezone-analysis', '❌ 时区不是美国时区', 'error');
            }
            
            // 检查时间一致性
            const localTime = now.toLocaleString();
            const utcTime = now.toUTCString();
            addResult('timezone-analysis', `🌍 本地时间: ${localTime}`, 'result');
            addResult('timezone-analysis', `🌐 UTC时间: ${utcTime}`, 'result');
        }
        
        function analyzeNetworkFingerprint() {
            clearResults('network-analysis');
            
            // 检查User-Agent
            const userAgent = navigator.userAgent;
            addResult('network-analysis', `🖥️ User-Agent: ${userAgent.substring(0, 100)}...`, 'result');
            
            // 检查语言设置
            const language = navigator.language;
            const languages = navigator.languages;
            addResult('network-analysis', `🗣️ 主语言: ${language}`, 'result');
            addResult('network-analysis', `🌐 支持语言: ${languages.join(', ')}`, 'result');
            
            if (language.startsWith('en-US')) {
                addResult('network-analysis', '✅ 语言设置正确 (en-US)', 'success');
            } else {
                addResult('network-analysis', '❌ 语言设置不是en-US', 'error');
            }
            
            // 检查Accept-Language头
            fetch('https://httpbin.org/headers')
                .then(response => response.json())
                .then(data => {
                    const acceptLanguage = data.headers['Accept-Language'];
                    addResult('network-analysis', `📡 Accept-Language: ${acceptLanguage}`, 'result');
                    
                    if (acceptLanguage && acceptLanguage.includes('en-US')) {
                        addResult('network-analysis', '✅ HTTP语言头正确', 'success');
                    } else {
                        addResult('network-analysis', '❌ HTTP语言头可能暴露真实位置', 'error');
                    }
                })
                .catch(error => {
                    addResult('network-analysis', '⚠️ 无法检查HTTP头', 'warning');
                });
        }
        
        function analyzeDeviceFingerprint() {
            clearResults('device-analysis');
            
            // 检查屏幕分辨率
            const screenWidth = screen.width;
            const screenHeight = screen.height;
            addResult('device-analysis', `📺 屏幕分辨率: ${screenWidth}x${screenHeight}`, 'result');
            
            // 检查硬件信息
            const hardwareConcurrency = navigator.hardwareConcurrency;
            addResult('device-analysis', `💻 CPU核心数: ${hardwareConcurrency}`, 'result');
            
            // 检查设备内存
            if (navigator.deviceMemory) {
                addResult('device-analysis', `🧠 设备内存: ${navigator.deviceMemory}GB`, 'result');
            }
            
            // 检查Canvas指纹
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('Canvas fingerprint test', 2, 2);
            const canvasFingerprint = canvas.toDataURL();
            addResult('device-analysis', `🎨 Canvas指纹: ${canvasFingerprint.substring(0, 50)}...`, 'result');
            
            // 检查WebGL指纹
            const gl = canvas.getContext('webgl');
            if (gl) {
                const vendor = gl.getParameter(gl.VENDOR);
                const renderer = gl.getParameter(gl.RENDERER);
                addResult('device-analysis', `🎮 WebGL厂商: ${vendor}`, 'result');
                addResult('device-analysis', `🎮 WebGL渲染器: ${renderer}`, 'result');
            }
        }
        
        function analyzeFraudDetection() {
            clearResults('fraud-analysis');
            
            // 检查常见的反欺诈服务
            const fraudServices = [
                'verisoul.ai',
                'sift.com',
                'riskified.com',
                'signifyd.com',
                'forter.com'
            ];
            
            addResult('fraud-analysis', '🔍 检查常见反欺诈服务...', 'result');
            
            fraudServices.forEach(service => {
                fetch(`https://${service}`, { mode: 'no-cors' })
                    .then(() => {
                        addResult('fraud-analysis', `⚠️ 检测到反欺诈服务: ${service}`, 'warning');
                    })
                    .catch(() => {
                        // 正常情况，服务不可达
                    });
            });
            
            // 检查localStorage中的追踪数据
            const trackingKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key.includes('track') || key.includes('fingerprint') || key.includes('device')) {
                    trackingKeys.push(key);
                }
            }
            
            if (trackingKeys.length > 0) {
                addResult('fraud-analysis', `⚠️ 发现追踪数据: ${trackingKeys.join(', ')}`, 'warning');
            } else {
                addResult('fraud-analysis', '✅ 未发现明显的追踪数据', 'success');
            }
        }
        
        function analyzeBehavior() {
            clearResults('behavior-analysis');
            
            // 检查鼠标移动模式
            let mouseMovements = 0;
            let lastMouseTime = Date.now();
            
            document.addEventListener('mousemove', function() {
                mouseMovements++;
                lastMouseTime = Date.now();
            });
            
            setTimeout(() => {
                if (mouseMovements > 0) {
                    addResult('behavior-analysis', `🖱️ 鼠标移动次数: ${mouseMovements}`, 'success');
                } else {
                    addResult('behavior-analysis', '⚠️ 未检测到鼠标移动 (可能被识别为机器人)', 'warning');
                }
            }, 3000);
            
            // 检查页面停留时间
            const pageLoadTime = Date.now();
            addResult('behavior-analysis', '⏱️ 开始监控页面行为...', 'result');
            
            // 检查滚动行为
            let scrollEvents = 0;
            window.addEventListener('scroll', function() {
                scrollEvents++;
            });
            
            setTimeout(() => {
                const stayTime = Date.now() - pageLoadTime;
                addResult('behavior-analysis', `⏱️ 页面停留时间: ${Math.round(stayTime/1000)}秒`, 'result');
                addResult('behavior-analysis', `📜 滚动事件: ${scrollEvents}次`, 'result');
                
                if (stayTime < 5000) {
                    addResult('behavior-analysis', '⚠️ 页面停留时间过短，可能被识别为机器人', 'warning');
                }
            }, 5000);
        }
        
        // 页面加载时自动运行基础检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                analyzeGeolocation();
                analyzeTimezone();
                analyzeDeviceFingerprint();
            }, 1000);
        });
    </script>
</body>
</html>
