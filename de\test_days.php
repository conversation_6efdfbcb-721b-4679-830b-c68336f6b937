<?php
// 测试天数计算功能
header('Content-Type: text/html; charset=utf-8');
require_once 'config.php';

$pdo = getDBConnection();
if (!$pdo) {
    die('数据库连接失败');
}

// 获取所有用户进行测试
$stmt = $pdo->prepare("
    SELECT 
        u.id,
        u.username,
        u.registration_date,
        u.trial_days,
        u.remaining_days as stored_remaining_days,
        u.status,
        COALESCE(
            (SELECT SUM(days_added) FROM activation_history WHERE user_id = u.id), 
            0
        ) as total_activated_days
    FROM users u
    ORDER BY u.registration_date DESC
");
$stmt->execute();
$users = $stmt->fetchAll();

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天数计算测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .expired { background-color: #ffebee; }
        .active { background-color: #e8f5e8; }
        .warning { background-color: #fff3e0; }
        .btn { padding: 5px 10px; margin: 2px; cursor: pointer; }
        .btn-update { background: #4CAF50; color: white; border: none; }
        .btn-test { background: #2196F3; color: white; border: none; }
    </style>
</head>
<body>
    <h1>🧮 用户天数计算测试</h1>
    
    <div style="margin-bottom: 20px;">
        <button class="btn btn-test" onclick="updateAllUsers()">🔄 更新所有用户天数</button>
        <button class="btn btn-test" onclick="location.reload()">🔄 刷新页面</button>
    </div>

    <table>
        <thead>
            <tr>
                <th>用户ID</th>
                <th>用户名</th>
                <th>注册日期</th>
                <th>已注册天数</th>
                <th>初始试用天数</th>
                <th>激活码增加天数</th>
                <th>总可用天数</th>
                <th>数据库存储天数</th>
                <th>实际计算天数</th>
                <th>状态</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($users as $user): ?>
                <?php
                // 计算已注册天数
                $registrationDate = new DateTime($user['registration_date']);
                $currentDate = new DateTime();
                $daysPassed = $registrationDate->diff($currentDate)->days;
                
                // 计算总可用天数
                $totalAvailableDays = $user['trial_days'] + $user['total_activated_days'];
                
                // 计算实际剩余天数
                $actualRemainingDays = max(0, $totalAvailableDays - $daysPassed);
                
                // 确定行的CSS类
                $rowClass = '';
                if ($actualRemainingDays <= 0) {
                    $rowClass = 'expired';
                } elseif ($actualRemainingDays <= 3) {
                    $rowClass = 'warning';
                } else {
                    $rowClass = 'active';
                }
                ?>
                <tr class="<?php echo $rowClass; ?>">
                    <td><?php echo $user['id']; ?></td>
                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                    <td><?php echo $user['registration_date']; ?></td>
                    <td><?php echo $daysPassed; ?> 天</td>
                    <td><?php echo $user['trial_days']; ?> 天</td>
                    <td><?php echo $user['total_activated_days']; ?> 天</td>
                    <td><?php echo $totalAvailableDays; ?> 天</td>
                    <td><?php echo $user['stored_remaining_days']; ?> 天</td>
                    <td><strong><?php echo $actualRemainingDays; ?> 天</strong></td>
                    <td><?php echo $user['status']; ?></td>
                    <td>
                        <button class="btn btn-update" onclick="updateUser(<?php echo $user['id']; ?>)">
                            更新
                        </button>
                    </td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <div style="margin-top: 20px;">
        <h3>📋 说明：</h3>
        <ul>
            <li><strong>已注册天数</strong>：从注册到现在经过的天数</li>
            <li><strong>总可用天数</strong>：初始试用天数 + 激活码增加的天数</li>
            <li><strong>实际计算天数</strong>：总可用天数 - 已注册天数（不小于0）</li>
            <li><strong>数据库存储天数</strong>：数据库中存储的remaining_days值</li>
            <li>🟢 绿色：剩余天数 > 3</li>
            <li>🟡 橙色：剩余天数 1-3</li>
            <li>🔴 红色：剩余天数 = 0（已过期）</li>
        </ul>
    </div>

    <script>
        async function updateUser(userId) {
            try {
                const response = await fetch(`user_status.php?user_id=${userId}`);
                const result = await response.json();
                if (result.success) {
                    alert(`用户 ${userId} 更新成功！剩余天数：${result.data.remaining_days}`);
                    location.reload();
                } else {
                    alert(`更新失败：${result.message}`);
                }
            } catch (error) {
                alert(`更新失败：${error.message}`);
            }
        }

        async function updateAllUsers() {
            const userIds = <?php echo json_encode(array_column($users, 'id')); ?>;
            let updated = 0;
            
            for (const userId of userIds) {
                try {
                    const response = await fetch(`user_status.php?user_id=${userId}`);
                    const result = await response.json();
                    if (result.success) {
                        updated++;
                    }
                } catch (error) {
                    console.error(`更新用户 ${userId} 失败:`, error);
                }
            }
            
            alert(`批量更新完成！成功更新 ${updated} 个用户`);
            location.reload();
        }
    </script>
</body>
</html>
