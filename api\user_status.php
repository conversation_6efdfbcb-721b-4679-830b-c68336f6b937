<?php
require_once 'config.php';

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendResponse(false, '只允许GET请求', null, 405);
}

// 获取用户ID
$userId = (int)($_GET['user_id'] ?? 0);

if ($userId <= 0) {
    sendResponse(false, '用户ID不能为空', null, 400);
}

// 连接数据库
$pdo = getDBConnection();
if (!$pdo) {
    sendResponse(false, '数据库连接失败', null, 500);
}

try {
    // 查询用户状态
    $stmt = $pdo->prepare("
        SELECT id, username, expires_at, status, last_login, created_at
        FROM users
        WHERE id = ?
    ");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();

    if (!$user) {
        sendResponse(false, '用户不存在', null, 404);
    }

    // 检查用户是否过期并更新状态（检查时间和状态）
    $isExpired = false;

    // 检查数据库状态
    if ($user['status'] === 'expired') {
        $isExpired = true;
    }
    // 检查到期时间
    else if ($user['expires_at']) {
        $currentTime = new DateTime();
        $expiryTime = new DateTime($user['expires_at']);
        $isExpired = $currentTime >= $expiryTime;
    } else {
        $isExpired = true;
    }

    // 如果时间过期但状态还是active，更新为expired
    if ($isExpired && $user['status'] === 'active') {
        $stmt = $pdo->prepare("UPDATE users SET status = 'expired' WHERE id = ?");
        $stmt->execute([$userId]);
        $user['status'] = 'expired';
    }

    // 计算剩余天数用于显示
    $remainingDays = 0;
    if ($user['expires_at'] && !$isExpired) {
        $currentTime = new DateTime();
        $expiryTime = new DateTime($user['expires_at']);
        $diff = $currentTime->diff($expiryTime);
        $remainingDays = $diff->days;
    }

    // 获取系统配置
    $purchaseLink = getSystemConfig($pdo, 'purchase_link');

    // 返回用户状态
    sendResponse(true, '获取用户状态成功', [
        'user_id' => $user['id'],
        'username' => $user['username'],
        'expires_at' => $user['expires_at'],
        'remaining_days' => $remainingDays,
        'status' => $user['status'],
        'last_login' => $user['last_login'],
        'created_at' => $user['created_at'],
        'purchase_link' => $purchaseLink,
        'is_expired' => $isExpired
    ]);

} catch (PDOException $e) {
    error_log("User status error: " . $e->getMessage());
    sendResponse(false, '获取用户状态失败', null, 500);
} catch (Exception $e) {
    error_log("User status error: " . $e->getMessage());
    sendResponse(false, '获取用户状态时发生错误', null, 500);
}
?>
