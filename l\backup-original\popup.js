// Global Timezone & Location Spoofer - Popup Script v2.9.7
document.addEventListener('DOMContentLoaded', async function() {
    // 首先检查用户认证状态
    const authResult = await checkUserAuthentication();
    if (authResult === 'expired') {
        // 用户过期，已显示过期页面，不需要跳转
        return;
    } else if (!authResult) {
        // 用户未登录，跳转到认证页面
        window.location.href = 'auth.html';
        return;
    }
    const regionCards = document.querySelectorAll('.region-card');
    const applyBtn = document.getElementById('apply-btn');
    const randomBtn = document.getElementById('random-btn');
    const cleanAugmentBtn = document.getElementById('clean-augment-btn');
    const augLoginBtn = document.getElementById('aug-login-btn');
    const cleanStatus = document.getElementById('clean-status');
    const currentLocation = document.getElementById('current-location');
    const currentTime = document.getElementById('current-time');
    const currentDetails = document.getElementById('current-details');
    const status = document.getElementById('status');
    const loading = document.getElementById('loading');
    const userWelcome = document.getElementById('user-welcome');
    const logoutBtn = document.getElementById('logout-btn');
    const tutorialBtn = document.getElementById('tutorialBtn');

    let selectedRegion = null;

    // 地区配置
    const regionConfig = {
        US: {
            name: '美国',
            flag: '🇺🇸',
            timezones: ['America/New_York', 'America/Chicago', 'America/Denver', 'America/Los_Angeles'],
            locale: 'en-US',
            country: 'United States'
        },
        TW: {
            name: '台湾',
            flag: '🇹🇼',
            timezones: ['Asia/Taipei'],
            locale: 'zh-TW',
            country: 'Taiwan'
        },
        JP: {
            name: '日本',
            flag: '🇯🇵',
            timezones: ['Asia/Tokyo'],
            locale: 'ja-JP',
            country: 'Japan'
        },
        SG: {
            name: '新加坡',
            flag: '🇸🇬',
            timezones: ['Asia/Singapore'],
            locale: 'en-SG',
            country: 'Singapore'
        }
    };

    // 初始化
    init();

    async function init() {
        await loadCurrentSettings();
        await loadUserInfo();
        setupEventListeners();
        startTimeUpdate();
    }

    // 加载当前设置
    async function loadCurrentSettings() {
        try {
            const result = await chrome.storage.sync.get(['selectedRegion', 'selectedCity', 'lastUpdate']);

            if (result.selectedRegion) {
                selectedRegion = result.selectedRegion;
                updateSelectedCard();
                updateStatus(result);
            } else {
                // 默认选择美国
                selectedRegion = 'US';
                updateSelectedCard();
                currentLocation.textContent = '未设置';
                currentDetails.textContent = '请选择一个地区并应用设置';
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
            currentLocation.textContent = '加载失败';
            currentDetails.textContent = '无法加载当前设置';
        }
    }

    // 更新选中的卡片
    function updateSelectedCard() {
        regionCards.forEach(card => {
            card.classList.remove('selected');
            if (card.dataset.region === selectedRegion) {
                card.classList.add('selected');
            }
        });
    }

    // 更新状态显示
    function updateStatus(settings) {
        if (settings.selectedCity && settings.selectedRegion) {
            const config = regionConfig[settings.selectedRegion];
            currentLocation.textContent = `${config.flag} ${settings.selectedCity.city}, ${settings.selectedCity.country}`;
            currentDetails.textContent = `时区: ${settings.selectedCity.timezone} | 语言: ${settings.selectedCity.locale}`;
            status.classList.add('active');
        }
    }

    // 设置事件监听器
    function setupEventListeners() {
        // 地区卡片点击
        regionCards.forEach(card => {
            card.addEventListener('click', () => {
                selectedRegion = card.dataset.region;
                updateSelectedCard();
            });
        });

        // 应用设置按钮
        applyBtn.addEventListener('click', applySettings);

        // 随机选择按钮
        randomBtn.addEventListener('click', randomSelect);

        // 清理Augment数据按钮
        cleanAugmentBtn.addEventListener('click', cleanAugmentData);

        // Aug登录按钮
        augLoginBtn.addEventListener('click', openAugmentLogin);

        // 退出登录按钮
        logoutBtn.addEventListener('click', handleLogout);

        // 使用教程按钮
        tutorialBtn.addEventListener('click', openTutorial);
    }

    // 应用设置
    async function applySettings() {
        if (!selectedRegion) {
            alert('请先选择一个地区');
            return;
        }

        showLoading(true);

        try {
            const config = regionConfig[selectedRegion];
            const selectedTimezone = config.timezones[Math.floor(Math.random() * config.timezones.length)];

            // 生成随机城市数据（这里简化处理，实际应该从数据库中选择）
            const cityData = generateCityData(selectedRegion, selectedTimezone, config);

            // 保存设置到chrome.storage
            await chrome.storage.sync.set({
                selectedRegion: selectedRegion,
                selectedCity: cityData,
                lastUpdate: Date.now()
            });

            // 同时保存到localStorage供content script使用
            const storageKey = 'globalTimezoneSpoofer_selectedCity';
            try {
                // 通知所有标签页保存设置到localStorage
                const tabs = await chrome.tabs.query({});
                for (const tab of tabs) {
                    try {
                        await chrome.scripting.executeScript({
                            target: { tabId: tab.id },
                            func: (key, data) => {
                                localStorage.setItem(key, JSON.stringify(data));
                            },
                            args: [storageKey, cityData]
                        });
                    } catch (e) {
                        // 忽略无法访问的标签页
                    }
                }
            } catch (e) {
                console.warn('Failed to save to localStorage:', e);
            }

            // 更新显示
            updateStatus({ selectedRegion, selectedCity: cityData });

            // 通知所有标签页刷新
            const tabs = await chrome.tabs.query({});
            for (const tab of tabs) {
                try {
                    await chrome.tabs.reload(tab.id);
                } catch (e) {
                    // 忽略无法刷新的标签页
                }
            }

            showLoading(false);

            // 显示成功消息
            showSuccessMessage();

        } catch (error) {
            console.error('Failed to apply settings:', error);
            alert('应用设置失败，请重试');
            showLoading(false);
        }
    }

    // 随机选择
    function randomSelect() {
        const regions = Object.keys(regionConfig);
        selectedRegion = regions[Math.floor(Math.random() * regions.length)];
        updateSelectedCard();
    }

    // 生成城市数据
    function generateCityData(region, timezone, config) {
        // 简化的城市数据生成，实际应该从完整数据库中选择
        const cityNames = {
            US: ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix'],
            TW: ['Taipei', 'Kaohsiung', 'Taichung', 'Tainan', 'Taoyuan'],
            JP: ['Tokyo', 'Osaka', 'Yokohama', 'Nagoya', 'Sapporo'],
            SG: ['Singapore', 'Jurong West', 'Woodlands', 'Tampines', 'Sengkang']
        };

        const coordinates = {
            US: { lat: 40.7128, lng: -74.0060 },
            TW: { lat: 25.0330, lng: 121.5654 },
            JP: { lat: 35.6762, lng: 139.6503 },
            SG: { lat: 1.3521, lng: 103.8198 }
        };

        const cities = cityNames[region];
        const coords = coordinates[region];
        const selectedCity = cities[Math.floor(Math.random() * cities.length)];

        return {
            city: selectedCity,
            state: region === 'US' ? 'NY' : config.name,
            country: config.country,
            country_code: region,
            timezone: timezone,
            locale: config.locale,
            lat: coords.lat + (Math.random() - 0.5) * 0.2,
            lng: coords.lng + (Math.random() - 0.5) * 0.2
        };
    }

    // 显示/隐藏加载状态
    function showLoading(show) {
        if (show) {
            loading.style.display = 'block';
            applyBtn.disabled = true;
            randomBtn.disabled = true;
        } else {
            loading.style.display = 'none';
            applyBtn.disabled = false;
            randomBtn.disabled = false;
        }
    }

    // 显示成功消息
    function showSuccessMessage() {
        const originalText = applyBtn.textContent;
        applyBtn.textContent = '✅ 设置已应用';
        applyBtn.style.background = '#4CAF50';

        setTimeout(() => {
            applyBtn.textContent = originalText;
            applyBtn.style.background = '';
        }, 2000);
    }

    // 开始时间更新
    function startTimeUpdate() {
        updateTime();
        setInterval(updateTime, 1000);
    }

    // 更新时间显示
    function updateTime() {
        const now = new Date();
        currentTime.textContent = now.toLocaleTimeString('zh-CN', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 清理Augment相关网站数据
    async function cleanAugmentData() {
        try {
            // 显示清理状态
            showCleanStatus('正在清理Augment相关数据...', 'progress');
            cleanAugmentBtn.disabled = true;

            // 使用通配符清理选项 - 更高效的方式
            const cleanOptions = {
                origins: [
                    'https://*.augmentcode.com',
                    'http://*.augmentcode.com',
                    'https://*.augment.com',
                    'http://*.augment.com',
                    'https://augmentcode.com',
                    'http://augmentcode.com',
                    'https://augment.com',
                    'http://augment.com'
                ]
            };

            // 清理各种类型的数据
            const cleanPromises = [];

            // 清理Cookies
            cleanPromises.push(
                chrome.browsingData.removeCookies(cleanOptions)
                    .catch(err => console.warn('清理Cookies失败:', err))
            );

            // 清理LocalStorage
            cleanPromises.push(
                chrome.browsingData.removeLocalStorage(cleanOptions)
                    .catch(err => console.warn('清理LocalStorage失败:', err))
            );

            // 清理Cache
            cleanPromises.push(
                chrome.browsingData.removeCache(cleanOptions)
                    .catch(err => console.warn('清理Cache失败:', err))
            );

            // 清理IndexedDB
            cleanPromises.push(
                chrome.browsingData.removeIndexedDB(cleanOptions)
                    .catch(err => console.warn('清理IndexedDB失败:', err))
            );

            // 清理WebSQL
            cleanPromises.push(
                chrome.browsingData.removeWebSQL(cleanOptions)
                    .catch(err => console.warn('清理WebSQL失败:', err))
            );

            // 等待所有清理操作完成
            await Promise.all(cleanPromises);

            // 额外清理：通过content script清理当前标签页的相关数据
            try {
                const tabs = await chrome.tabs.query({});
                for (const tab of tabs) {
                    // 使用更精确的域名匹配
                    if (tab.url && (
                        tab.url.includes('augmentcode.com') ||
                        tab.url.includes('augment.com')
                    )) {
                        try {
                            await chrome.scripting.executeScript({
                                target: { tabId: tab.id },
                                func: () => {
                                    // 清理当前页面的localStorage和sessionStorage
                                    try {
                                        localStorage.clear();
                                        sessionStorage.clear();
                                        console.log('🧹 页面存储数据已清理');
                                    } catch (e) {
                                        console.warn('清理页面存储失败:', e);
                                    }
                                }
                            });
                        } catch (e) {
                            // 忽略无法访问的标签页
                        }
                    }
                }
            } catch (e) {
                console.warn('清理标签页数据失败:', e);
            }

            // 显示成功状态
            showCleanStatus('✅ Augment数据清理完成！', 'success');

            // 3秒后隐藏状态
            setTimeout(() => {
                hideCleanStatus();
            }, 3000);

        } catch (error) {
            console.error('清理Augment数据失败:', error);
            showCleanStatus('❌ 清理失败，请重试', 'error');

            // 3秒后隐藏错误状态
            setTimeout(() => {
                hideCleanStatus();
            }, 3000);
        } finally {
            cleanAugmentBtn.disabled = false;
        }
    }

    // 显示清理状态
    function showCleanStatus(message, type) {
        cleanStatus.style.display = 'block';
        cleanStatus.className = `clean-status ${type}`;

        if (type === 'progress') {
            cleanStatus.innerHTML = `<div class="clean-progress">${message}</div>`;
        } else {
            cleanStatus.innerHTML = `<div>${message}</div>`;
        }
    }

    // 隐藏清理状态
    function hideCleanStatus() {
        cleanStatus.style.display = 'none';
    }

    // 用户认证检查函数
    async function checkUserAuthentication() {
        try {
            // 从本地存储获取用户数据
            const userData = await getStoredUserData();
            if (!userData || !userData.user_id) {
                return false;
            }

            // 验证用户状态
            const API_BASE = 'http://103.96.75.196/api';
            const response = await fetch(`${API_BASE}/user_status.php?user_id=${userData.user_id}`);
            const result = await response.json();

            if (result.success && result.data.remaining_days > 0 && result.data.status === 'active') {
                // 更新本地存储的用户数据
                await storeUserData(result.data);
                return true;
            } else if (result.success && (result.data.remaining_days <= 0 || result.data.status === 'expired')) {
                // 用户已过期（检查剩余天数或状态），显示购买提示
                showExpiredUserMessage(result.data);
                return 'expired'; // 返回特殊状态表示已过期
            } else {
                // 清除无效的用户数据
                await clearStoredUserData();
                return false;
            }
        } catch (error) {
            console.error('认证检查失败:', error);
            return false;
        }
    }

    // 存储用户数据
    async function storeUserData(userData) {
        return new Promise((resolve) => {
            chrome.storage.local.set({ 'user_data': userData }, resolve);
        });
    }

    // 获取存储的用户数据
    async function getStoredUserData() {
        return new Promise((resolve) => {
            chrome.storage.local.get(['user_data'], (result) => {
                resolve(result.user_data || null);
            });
        });
    }

    // 清除存储的用户数据
    async function clearStoredUserData() {
        return new Promise((resolve) => {
            chrome.storage.local.remove(['user_data'], resolve);
        });
    }

    // 显示过期用户消息
    function showExpiredUserMessage(userData) {
        // 隐藏主界面
        document.body.innerHTML = `
            <div style="
                padding: 20px;
                text-align: center;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                min-height: 400px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            ">
                <div style="
                    background: rgba(255, 255, 255, 0.1);
                    backdrop-filter: blur(10px);
                    border-radius: 15px;
                    padding: 30px;
                    max-width: 350px;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                ">
                    <div style="font-size: 48px; margin-bottom: 20px;">⏰</div>
                    <h2 style="margin: 0 0 15px 0; font-size: 24px;">账户已过期</h2>
                    <p style="margin: 0 0 25px 0; font-size: 16px; line-height: 1.5;">
                        您的试用期已结束，请购买激活码继续使用
                    </p>

                    <div style="margin-bottom: 20px;">
                        <button id="purchaseBtn" style="
                            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
                            color: white;
                            border: none;
                            padding: 12px 30px;
                            border-radius: 25px;
                            font-size: 16px;
                            font-weight: bold;
                            cursor: pointer;
                            width: 100%;
                            margin-bottom: 15px;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
                        " onmouseover="this.style.transform='translateY(-2px)'"
                           onmouseout="this.style.transform='translateY(0)'">
                            🛒 立即购买激活码
                        </button>
                    </div>

                    <!-- 激活码输入区域 -->
                    <div style="
                        background: rgba(255, 255, 255, 0.1);
                        border-radius: 10px;
                        padding: 20px;
                        margin-top: 15px;
                    ">
                        <h3 style="margin: 0 0 15px 0; font-size: 16px;">✨ 输入激活码</h3>
                        <input type="text" id="activationCodeInput" placeholder="请输入激活码" style="
                            width: 100%;
                            padding: 10px;
                            border: none;
                            border-radius: 8px;
                            font-size: 14px;
                            text-align: center;
                            letter-spacing: 2px;
                            font-family: monospace;
                            margin-bottom: 15px;
                            box-sizing: border-box;
                        ">
                        <button id="activateBtn" style="
                            background: rgba(255, 255, 255, 0.2);
                            color: white;
                            border: 1px solid rgba(255, 255, 255, 0.3);
                            padding: 10px 25px;
                            border-radius: 20px;
                            font-size: 14px;
                            cursor: pointer;
                            width: 100%;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.background='rgba(255, 255, 255, 0.3)'"
                           onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'">
                            激活
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 绑定按钮事件
        document.getElementById('purchaseBtn').addEventListener('click', () => {
            if (userData.purchase_link && userData.purchase_link !== '#') {
                chrome.tabs.create({ url: userData.purchase_link });
            } else {
                alert('购买链接暂未配置，请联系客服');
            }
        });

        // 激活码输入和激活按钮事件
        const activationCodeInput = document.getElementById('activationCodeInput');
        const activateBtn = document.getElementById('activateBtn');

        // 激活按钮点击事件
        activateBtn.addEventListener('click', async () => {
            const code = activationCodeInput.value.trim();

            if (!code) {
                alert('请输入激活码');
                return;
            }

            // 设置按钮为加载状态
            activateBtn.disabled = true;
            activateBtn.textContent = '激活中...';

            try {
                const response = await fetch('http://103.96.75.196/api/activate.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: userData.user_id || userData.id,
                        activation_code: code
                    })
                });

                const result = await response.json();

                if (result.success) {
                    alert(`激活成功！增加了${result.data.days_added}天使用时间`);
                    // 激活成功后重新加载页面
                    location.reload();
                } else {
                    alert(result.message || '激活失败');
                }
            } catch (error) {
                console.error('激活失败:', error);
                alert('激活失败，请检查网络连接');
            } finally {
                activateBtn.disabled = false;
                activateBtn.textContent = '激活';
            }
        });

        // 回车键激活
        activationCodeInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                activateBtn.click();
            }
        });
    }

    // 加载用户信息
    async function loadUserInfo() {
        try {
            const userData = await getStoredUserData();
            if (userData && userData.username) {
                const remainingText = userData.remaining_days > 0
                    ? `剩余${userData.remaining_days}天`
                    : '已过期';
                userWelcome.textContent = `👤 ${userData.username} (${remainingText})`;

                // 根据剩余天数设置颜色
                if (userData.remaining_days <= 0) {
                    userWelcome.style.color = '#f44336';
                } else if (userData.remaining_days <= 3) {
                    userWelcome.style.color = '#FFC107';
                } else {
                    userWelcome.style.color = '#4CAF50';
                }
            } else {
                userWelcome.textContent = '👤 未登录';
            }
        } catch (error) {
            console.error('加载用户信息失败:', error);
            userWelcome.textContent = '👤 加载失败';
        }
    }

    // 处理退出登录
    async function handleLogout() {
        if (confirm('确定要退出登录吗？')) {
            try {
                await clearStoredUserData();
                // 跳转到认证页面
                window.location.href = 'auth.html';
            } catch (error) {
                console.error('退出登录失败:', error);
                alert('退出登录失败，请重试');
            }
        }
    }

    // 打开Augment登录页面
    async function openAugmentLogin() {
        try {
            // 获取当前活动标签页
            const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
            const currentTab = tabs[0];

            if (currentTab) {
                // 在当前标签页打开Augment登录页面
                await chrome.tabs.update(currentTab.id, {
                    url: 'https://app.augmentcode.com'
                });

                // 关闭插件弹窗
                window.close();
            }
        } catch (error) {
            console.error('打开Augment登录页面失败:', error);

            // 如果上述方法失败，尝试创建新标签页
            try {
                await chrome.tabs.create({
                    url: 'https://app.augmentcode.com'
                });
                window.close();
            } catch (fallbackError) {
                console.error('创建新标签页失败:', fallbackError);
            }
        }
    }

    // 打开使用教程
    async function openTutorial() {
        try {
            // 从数据库获取教程链接
            const API_BASE = 'http://103.96.75.196/api';
            const response = await fetch(`${API_BASE}/get_config.php?key=usage_tutorial`);
            const result = await response.json();

            let tutorialUrl = 'https://example.com/tutorial'; // 默认链接

            if (result.success && result.data && result.data.config_value) {
                tutorialUrl = result.data.config_value;
            }

            // 在新标签页打开教程链接
            await chrome.tabs.create({ url: tutorialUrl });

            // 关闭插件弹窗
            window.close();

        } catch (error) {
            console.error('打开使用教程失败:', error);

            // 如果获取配置失败，使用默认链接
            try {
                await chrome.tabs.create({ url: 'https://example.com/tutorial' });
                window.close();
            } catch (fallbackError) {
                console.error('打开默认教程链接失败:', fallbackError);
                alert('无法打开使用教程，请稍后重试');
            }
        }
    }
});
