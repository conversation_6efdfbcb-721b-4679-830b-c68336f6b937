# Timezone Extension API - Apache配置

# 启用重写引擎
RewriteEngine On

# 允许跨域请求
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With, X-Custom-Header"
Header always set Access-Control-Allow-Credentials "false"

# 处理预检请求
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# 设置默认字符集
AddDefaultCharset UTF-8

# 启用压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# 安全设置
<Files "*.php">
    Order allow,deny
    Allow from all
</Files>

# 隐藏敏感文件
<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

<Files "*.txt">
    Order allow,deny
    Deny from all
</Files>

# 错误页面
ErrorDocument 404 "API endpoint not found"
ErrorDocument 500 "Internal server error"

# 缓存设置
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType application/json "access plus 0 seconds"
</IfModule>
