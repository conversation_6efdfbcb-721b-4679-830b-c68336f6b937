<?php
require_once 'config.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, '只允许POST请求', null, 405);
}

// 获取输入数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    sendResponse(false, '无效的JSON数据', null, 400);
}

// 验证必需字段
$userId = (int)($input['user_id'] ?? 0);
$activationCode = trim($input['activation_code'] ?? '');

if ($userId <= 0 || empty($activationCode)) {
    sendResponse(false, '用户ID和激活码不能为空', null, 400);
}

// 激活码格式验证已移除，由数据库验证决定

// 连接数据库
$pdo = getDBConnection();
if (!$pdo) {
    sendResponse(false, '数据库连接失败', null, 500);
}

try {
    // 开始事务
    $pdo->beginTransaction();

    // 验证用户是否存在
    $stmt = $pdo->prepare("SELECT id, username, expires_at, status, used_activation_codes FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();

    if (!$user) {
        $pdo->rollBack();
        sendResponse(false, '用户不存在', null, 404);
    }

    // 检查激活码是否已被该用户使用过
    $usedCodes = $user['used_activation_codes'] ? explode(',', $user['used_activation_codes']) : [];
    if (in_array($activationCode, $usedCodes)) {
        $pdo->rollBack();
        sendResponse(false, '该激活码已被使用过', null, 409);
    }

    // 连接激活码数据库验证激活码
    $activationPdo = getActivationDBConnection();
    if (!$activationPdo) {
        $pdo->rollBack();
        sendResponse(false, '您已经使用过此激活码或激活码不存在', null, 404);
    }

    // 查询激活码
    $stmt = $activationPdo->prepare("
        SELECT id, code, use_times, is_used, paid, remark
        FROM activation_codes
        WHERE code = ? AND remark = 'timezone' AND paid = 1
    ");
    $stmt->execute([$activationCode]);
    $codeData = $stmt->fetch();

    if (!$codeData) {
        $pdo->rollBack();
        sendResponse(false, '您已经使用过此激活码或激活码不存在', null, 404);
    }

    // 检查激活码是否已使用
    if ($codeData['is_used'] == 1) {
        $pdo->rollBack();
        sendResponse(false, '您已经使用过此激活码或激活码不存在', null, 409);
    }

    $daysToAdd = (int)$codeData['use_times'];

    // 延长用户到期时间（简化版本）
    try {
        $currentTime = new DateTime();
        $currentExpiry = $user['expires_at'] ? new DateTime($user['expires_at']) : null;

        // 如果当前到期时间晚于现在，在原基础上延长；否则从现在开始计算
        $baseTime = ($currentExpiry && $currentExpiry > $currentTime) ? $currentExpiry : $currentTime;
        $newExpiry = clone $baseTime;
        $newExpiry->add(new DateInterval("P{$daysToAdd}D"));
        $newExpiryDate = $newExpiry->format('Y-m-d H:i:s');
    } catch (Exception $dateError) {
        $pdo->rollBack();
        error_log("Date calculation error: " . $dateError->getMessage());
        sendResponse(false, '日期计算错误', null, 500);
    }

    // 更新已使用的激活码列表
    $newUsedCodes = $usedCodes;
    $newUsedCodes[] = $activationCode;
    $usedCodesString = implode(',', $newUsedCodes);

    // 更新用户到期时间和已使用激活码
    $stmt = $pdo->prepare("
        UPDATE users
        SET expires_at = ?, status = 'active', used_activation_codes = ?, updated_at = NOW()
        WHERE id = ?
    ");
    $stmt->execute([$newExpiryDate, $usedCodesString, $userId]);

    // 计算新的剩余天数用于显示
    $currentTime = new DateTime();
    $expiryTime = new DateTime($newExpiryDate);
    $newRemainingDays = 0;
    if ($currentTime < $expiryTime) {
        $diff = $currentTime->diff($expiryTime);
        $newRemainingDays = $diff->days;
    }

    // 提交主数据库事务
    $pdo->commit();

    // 在主事务成功后，更新激活码数据库（避免事务冲突）
    try {
        $stmt = $activationPdo->prepare("
            UPDATE activation_codes
            SET is_used = 1
            WHERE id = ?
        ");
        $stmt->execute([$codeData['id']]);

        // 记录激活历史（使用本地数据库）
        $localPdo = getDBConnection();
        if ($localPdo) {
            $stmt = $localPdo->prepare("
                INSERT INTO activation_history (user_id, activation_code_id, days_added, ip_address)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([
                $userId,
                $codeData['id'], // 使用激活码数据库中的ID
                $daysToAdd,
                getClientIP()
            ]);
        }
    } catch (Exception $activationUpdateError) {
        // 记录错误但不影响主流程
        error_log("Failed to update activation code status: " . $activationUpdateError->getMessage());
    }

    // 返回成功响应
    sendResponse(true, '激活成功', [
        'user_id' => $userId,
        'username' => $user['username'],
        'days_added' => $daysToAdd,
        'expires_at' => $newExpiryDate,
        'remaining_days' => $newRemainingDays,
        'status' => 'active'
    ]);

} catch (PDOException $e) {
    $pdo->rollBack();
    error_log("Activation error: " . $e->getMessage());
    sendResponse(false, '激活失败，请稍后重试', null, 500);
} catch (Exception $e) {
    $pdo->rollBack();
    error_log("Activation error: " . $e->getMessage());
    sendResponse(false, '激活过程中发生错误', null, 500);
}
?>
