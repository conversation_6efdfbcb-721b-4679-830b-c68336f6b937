# 🌍 Global Timezone & Location Spoofer v2.9.0

## 功能特性

### ✨ 新功能 - 用户地区选择
- 🎯 **可选择地区**：支持美国🇺🇸、台湾🇹🇼、日本🇯🇵、新加坡🇸🇬
- 🔄 **持久化设置**：选择的地区在所有页面保持一致
- 🎛️ **友好界面**：点击扩展图标即可选择地区
- 📍 **精确模拟**：每个地区都有准确的时区、语言、坐标数据

### 🛡️ 核心功能
- ⏰ **时区伪装**：动态计算时区偏移，支持夏令时
- 📍 **地理位置伪装**：精确的GPS坐标模拟
- 🗣️ **语言伪装**：根据地区自动设置语言和locale
- 🌐 **网络指纹伪装**：User-Agent、分辨率、硬件信息等
- 🔒 **网站兼容性**：对敏感网站和Augment网站特殊处理

## 安装方法

### 方法一：开发者模式安装（推荐）

1. **打开Chrome扩展管理页面**
   - 在地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

3. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择包含所有文件的文件夹

4. **验证安装**
   - 扩展图标应该出现在工具栏中
   - 点击图标打开设置面板

## 使用方法

### 🎯 选择地区

1. **打开设置面板**
   - 点击浏览器工具栏中的扩展图标

2. **选择目标地区**
   - 🇺🇸 美国：多个时区支持（东部、中部、山地、太平洋时间）
   - 🇹🇼 台湾：台北时区，繁体中文
   - 🇯🇵 日本：东京时区，日语
   - 🇸🇬 新加坡：新加坡时区，英语

3. **应用设置**
   - 点击"应用设置"按钮
   - 所有标签页会自动刷新
   - 新设置立即生效

### 🔄 随机选择
- 点击"随机选择"按钮可随机选择一个地区
- 适合需要经常切换地区的用户

## 测试验证

### 🧪 使用测试页面
1. 打开 `region-test.html` 文件
2. 查看当前设置和各项测试结果
3. 切换地区后观察变化

### 🌐 在线测试网站
- https://webbrowsertools.com/timezone/
- https://whatismyipaddress.com/
- https://www.timeanddate.com/worldclock/

### 📊 验证项目
- ✅ 时区显示正确
- ✅ 地理位置坐标准确
- ✅ 语言设置匹配地区
- ✅ 时间格式符合当地习惯
- ✅ 所有页面设置一致

## 支持的地区详情

### 🇺🇸 美国
- **时区**：America/New_York, America/Chicago, America/Denver, America/Los_Angeles
- **语言**：en-US
- **城市**：纽约、洛杉矶、芝加哥、休斯顿、凤凰城等22个主要城市

### 🇹🇼 台湾
- **时区**：Asia/Taipei
- **语言**：zh-TW
- **城市**：台北、高雄、台中、台南、桃园

### 🇯🇵 日本
- **时区**：Asia/Tokyo
- **语言**：ja-JP
- **城市**：东京、大阪、横滨、名古屋、札幌、福冈、神户、京都

### 🇸🇬 新加坡
- **时区**：Asia/Singapore
- **语言**：en-SG
- **城市**：新加坡、裕廊西、兀兰、淡滨尼、盛港

## 技术特性

### 🔧 时区处理
- 动态计算时区偏移，支持夏令时自动转换
- 修复了Date API的时间计算错误
- 确保所有时间相关API返回一致结果

### 📍 地理位置
- 每个城市都有精确的GPS坐标
- 添加随机偏移模拟真实位置变化
- 精度控制在20-70米范围内

### 🗣️ 语言本地化
- 根据选定地区自动设置navigator.language
- 支持多语言环境的navigator.languages
- 数字、日期格式自动本地化

### 🛡️ 兼容性保护
- 对GitHub、StackOverflow等敏感网站跳过部分功能
- 对Augment网站特殊处理，避免Cloudflare冲突
- 保持核心功能的同时确保网站正常使用

## 版本历史

### v2.9.0 (当前版本)
- ✨ 新增用户地区选择功能
- 🎛️ 添加可视化设置面板
- 🔄 实现设置持久化存储
- 📱 优化用户界面体验

### v2.8.0
- 🌍 添加台湾、日本、新加坡支持
- 🔧 修复时区一致性问题
- 📊 扩展城市数据库

### v2.7.4
- 🛡️ 修复Augment网站兼容性
- 🔒 添加敏感网站保护

## 作者信息

👨‍💻 **作者**：小鱼游水  
🌐 **网址**：https://xoxome.online  
📧 **反馈**：如有问题或建议，欢迎联系

---

**注意**：本扩展仅供学习和测试用途，请遵守相关网站的使用条款和当地法律法规。
