{"version": 3, "file": "PhoneNumber.test.js", "names": ["metadata", "type", "PhoneNumber", "describe", "it", "phoneNumber", "setExt", "expect", "country", "to", "be", "undefined", "countryCallingCode", "should", "equal", "nationalNumber", "formatNational", "number", "ext", "format", "formatExtension", "extension", "isEqual", "isNonGeographic", "getPossibleCountries", "deep", "indexOf", "length"], "sources": ["../source/PhoneNumber.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' assert { type: 'json' }\r\nimport PhoneNumber from './PhoneNumber.js'\r\n\r\ndescribe('PhoneNumber', () => {\r\n\tit('should create a phone number via a public constructor', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('+78005553535', metadata)\r\n\t\tphoneNumber.setExt('1234')\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.countryCallingCode.should.equal('7')\r\n\t\tphoneNumber.nationalNumber.should.equal('8005553535')\r\n\t\tphoneNumber.formatNational().should.equal('8 (800) 555-35-35 ext. 1234')\r\n\t})\r\n\r\n\tit('should validate constructor arguments (public constructor)', () => {\r\n\t\texpect(() => new PhoneNumber()).to.throw('argument is required')\r\n\t\texpect(() => new PhoneNumber(undefined, metadata)).to.throw('argument is required')\r\n\t\texpect(() => new PhoneNumber('7', metadata)).to.throw('must consist of a \"+\"')\r\n\t\texpect(() => new PhoneNumber('+7', metadata)).to.throw('too short')\r\n\t\texpect(() => new PhoneNumber('+7800')).to.throw('`metadata` argument not passed')\r\n\t\texpect(() => new PhoneNumber(1234567890)).to.throw('must be a string')\r\n\t\texpect(() => new PhoneNumber('+1', 1234567890)).to.throw('must be a string')\r\n\t})\r\n\r\n\tit('should validate constructor arguments (private constructor)', () => {\r\n\t\texpect(() => new PhoneNumber(undefined, '800', metadata)).to.throw('First argument is required')\r\n\t\texpect(() => new PhoneNumber('7', undefined, metadata)).to.throw('`nationalNumber` argument is required')\r\n\t\texpect(() => new PhoneNumber('7', '8005553535')).to.throw('`metadata` argument not passed')\r\n\t})\r\n\r\n\tit('should accept country code argument', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('RU', '8005553535', metadata)\r\n\t\tphoneNumber.countryCallingCode.should.equal('7')\r\n\t\tphoneNumber.country.should.equal('RU')\r\n\t\tphoneNumber.number.should.equal('+78005553535')\r\n\t})\r\n\r\n\tit('should format number with options', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('7', '8005553535', metadata)\r\n\t\tphoneNumber.ext = '123'\r\n\t\tphoneNumber.format('NATIONAL', {\r\n\t\t\tformatExtension: (number, extension) => `${number} доб. ${extension}`\r\n\t\t})\r\n\t\t.should.equal('8 (800) 555-35-35 доб. 123')\r\n\t})\r\n\r\n\tit('should compare phone numbers', () => {\r\n\t\tnew PhoneNumber('RU', '8005553535', metadata).isEqual(new PhoneNumber('RU', '8005553535', metadata)).should.equal(true)\r\n\t\tnew PhoneNumber('RU', '8005553535', metadata).isEqual(new PhoneNumber('7', '8005553535', metadata)).should.equal(true)\r\n\t\tnew PhoneNumber('RU', '8005553535', metadata).isEqual(new PhoneNumber('RU', '8005553536', metadata)).should.equal(false)\r\n\t})\r\n\r\n\tit('should tell if a number is non-geographic', () => {\r\n\t\tnew PhoneNumber('7', '8005553535', metadata).isNonGeographic().should.equal(false)\r\n\t\tnew PhoneNumber('870', '773111632', metadata).isNonGeographic().should.equal(true)\r\n\t})\r\n\r\n\tit('should allow setting extension', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('1', '2133734253', metadata)\r\n\t\tphoneNumber.setExt('1234')\r\n\t\tphoneNumber.ext.should.equal('1234')\r\n\t\tphoneNumber.formatNational().should.equal('(************* ext. 1234')\r\n\t})\r\n\r\n\tit('should return possible countries', () => {\r\n      // \"599\": [\r\n      //    \"CW\", //  \"possible_lengths\": [7, 8]\r\n      //    \"BQ\" //  \"possible_lengths\": [7]\r\n      // ]\r\n\r\n\t\tlet phoneNumber = new PhoneNumber('599', '123456', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.getPossibleCountries().should.deep.equal([])\r\n\r\n\t\tphoneNumber = new PhoneNumber('599', '1234567', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.getPossibleCountries().should.deep.equal(['CW', 'BQ'])\r\n\r\n\t\tphoneNumber = new PhoneNumber('599', '12345678', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.getPossibleCountries().should.deep.equal(['CW'])\r\n\r\n\t\tphoneNumber = new PhoneNumber('599', '123456789', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.getPossibleCountries().should.deep.equal([])\r\n\t})\r\n\r\n\tit('should return possible countries in case of ambiguity', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('1', '2223334444', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.getPossibleCountries().indexOf('US').should.equal(0)\r\n\t\tphoneNumber.getPossibleCountries().length.should.equal(25)\r\n\t})\r\n\r\n\t// it('should return empty possible countries when no national number has been input', () => {\r\n\t// \tconst phoneNumber = new PhoneNumber('1', '', metadata)\r\n\t// \texpect(phoneNumber.country).to.be.undefined\r\n\t// \tphoneNumber.getPossibleCountries().should.deep.equal([])\r\n\t// })\r\n\r\n\tit('should return empty possible countries when not enough national number digits have been input', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('1', '222', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.getPossibleCountries().should.deep.equal([])\r\n\t})\r\n\r\n\tit('should return possible countries in case of no ambiguity', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('US', '2133734253', metadata)\r\n\t\tphoneNumber.country.should.equal('US')\r\n\t\tphoneNumber.getPossibleCountries().should.deep.equal(['US'])\r\n\t})\r\n\r\n\tit('should return empty possible countries in case of an unknown calling code', () => {\r\n\t\tconst phoneNumber = new PhoneNumber('777', '123', metadata)\r\n\t\texpect(phoneNumber.country).to.be.undefined\r\n\t\tphoneNumber.getPossibleCountries().should.deep.equal([])\r\n\t})\r\n\r\n\t// it('should validate phone number length', () => {\r\n\t// \tconst phoneNumber = new PhoneNumber('RU', '800', metadata)\r\n\t// \texpect(phoneNumber.validateLength()).to.equal('TOO_SHORT')\r\n\t//\r\n\t// \tconst phoneNumberValid = new PhoneNumber('RU', '8005553535', metadata)\r\n\t// \texpect(phoneNumberValid.validateLength()).to.be.undefined\r\n\t// })\r\n})"], "mappings": "AAAA,OAAOA,QAAP,MAAqB,sBAArB,UAAqDC,IAAI,EAAE,MAA3D;AACA,OAAOC,WAAP,MAAwB,kBAAxB;AAEAC,QAAQ,CAAC,aAAD,EAAgB,YAAM;EAC7BC,EAAE,CAAC,uDAAD,EAA0D,YAAM;IACjE,IAAMC,WAAW,GAAG,IAAIH,WAAJ,CAAgB,cAAhB,EAAgCF,QAAhC,CAApB;IACAK,WAAW,CAACC,MAAZ,CAAmB,MAAnB;IACAC,MAAM,CAACF,WAAW,CAACG,OAAb,CAAN,CAA4BC,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAN,WAAW,CAACO,kBAAZ,CAA+BC,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C;IACAT,WAAW,CAACU,cAAZ,CAA2BF,MAA3B,CAAkCC,KAAlC,CAAwC,YAAxC;IACAT,WAAW,CAACW,cAAZ,GAA6BH,MAA7B,CAAoCC,KAApC,CAA0C,6BAA1C;EACA,CAPC,CAAF;EASAV,EAAE,CAAC,4DAAD,EAA+D,YAAM;IACtEG,MAAM,CAAC;MAAA,OAAM,IAAIL,WAAJ,EAAN;IAAA,CAAD,CAAN,CAAgCO,EAAhC,UAAyC,sBAAzC;IACAF,MAAM,CAAC;MAAA,OAAM,IAAIL,WAAJ,CAAgBS,SAAhB,EAA2BX,QAA3B,CAAN;IAAA,CAAD,CAAN,CAAmDS,EAAnD,UAA4D,sBAA5D;IACAF,MAAM,CAAC;MAAA,OAAM,IAAIL,WAAJ,CAAgB,GAAhB,EAAqBF,QAArB,CAAN;IAAA,CAAD,CAAN,CAA6CS,EAA7C,UAAsD,uBAAtD;IACAF,MAAM,CAAC;MAAA,OAAM,IAAIL,WAAJ,CAAgB,IAAhB,EAAsBF,QAAtB,CAAN;IAAA,CAAD,CAAN,CAA8CS,EAA9C,UAAuD,WAAvD;IACAF,MAAM,CAAC;MAAA,OAAM,IAAIL,WAAJ,CAAgB,OAAhB,CAAN;IAAA,CAAD,CAAN,CAAuCO,EAAvC,UAAgD,gCAAhD;IACAF,MAAM,CAAC;MAAA,OAAM,IAAIL,WAAJ,CAAgB,UAAhB,CAAN;IAAA,CAAD,CAAN,CAA0CO,EAA1C,UAAmD,kBAAnD;IACAF,MAAM,CAAC;MAAA,OAAM,IAAIL,WAAJ,CAAgB,IAAhB,EAAsB,UAAtB,CAAN;IAAA,CAAD,CAAN,CAAgDO,EAAhD,UAAyD,kBAAzD;EACA,CARC,CAAF;EAUAL,EAAE,CAAC,6DAAD,EAAgE,YAAM;IACvEG,MAAM,CAAC;MAAA,OAAM,IAAIL,WAAJ,CAAgBS,SAAhB,EAA2B,KAA3B,EAAkCX,QAAlC,CAAN;IAAA,CAAD,CAAN,CAA0DS,EAA1D,UAAmE,4BAAnE;IACAF,MAAM,CAAC;MAAA,OAAM,IAAIL,WAAJ,CAAgB,GAAhB,EAAqBS,SAArB,EAAgCX,QAAhC,CAAN;IAAA,CAAD,CAAN,CAAwDS,EAAxD,UAAiE,uCAAjE;IACAF,MAAM,CAAC;MAAA,OAAM,IAAIL,WAAJ,CAAgB,GAAhB,EAAqB,YAArB,CAAN;IAAA,CAAD,CAAN,CAAiDO,EAAjD,UAA0D,gCAA1D;EACA,CAJC,CAAF;EAMAL,EAAE,CAAC,qCAAD,EAAwC,YAAM;IAC/C,IAAMC,WAAW,GAAG,IAAIH,WAAJ,CAAgB,IAAhB,EAAsB,YAAtB,EAAoCF,QAApC,CAApB;IACAK,WAAW,CAACO,kBAAZ,CAA+BC,MAA/B,CAAsCC,KAAtC,CAA4C,GAA5C;IACAT,WAAW,CAACG,OAAZ,CAAoBK,MAApB,CAA2BC,KAA3B,CAAiC,IAAjC;IACAT,WAAW,CAACY,MAAZ,CAAmBJ,MAAnB,CAA0BC,KAA1B,CAAgC,cAAhC;EACA,CALC,CAAF;EAOAV,EAAE,CAAC,mCAAD,EAAsC,YAAM;IAC7C,IAAMC,WAAW,GAAG,IAAIH,WAAJ,CAAgB,GAAhB,EAAqB,YAArB,EAAmCF,QAAnC,CAApB;IACAK,WAAW,CAACa,GAAZ,GAAkB,KAAlB;IACAb,WAAW,CAACc,MAAZ,CAAmB,UAAnB,EAA+B;MAC9BC,eAAe,EAAE,yBAACH,MAAD,EAASI,SAAT;QAAA,iBAA0BJ,MAA1B,kCAAyCI,SAAzC;MAAA;IADa,CAA/B,EAGCR,MAHD,CAGQC,KAHR,CAGc,4BAHd;EAIA,CAPC,CAAF;EASAV,EAAE,CAAC,8BAAD,EAAiC,YAAM;IACxC,IAAIF,WAAJ,CAAgB,IAAhB,EAAsB,YAAtB,EAAoCF,QAApC,EAA8CsB,OAA9C,CAAsD,IAAIpB,WAAJ,CAAgB,IAAhB,EAAsB,YAAtB,EAAoCF,QAApC,CAAtD,EAAqGa,MAArG,CAA4GC,KAA5G,CAAkH,IAAlH;IACA,IAAIZ,WAAJ,CAAgB,IAAhB,EAAsB,YAAtB,EAAoCF,QAApC,EAA8CsB,OAA9C,CAAsD,IAAIpB,WAAJ,CAAgB,GAAhB,EAAqB,YAArB,EAAmCF,QAAnC,CAAtD,EAAoGa,MAApG,CAA2GC,KAA3G,CAAiH,IAAjH;IACA,IAAIZ,WAAJ,CAAgB,IAAhB,EAAsB,YAAtB,EAAoCF,QAApC,EAA8CsB,OAA9C,CAAsD,IAAIpB,WAAJ,CAAgB,IAAhB,EAAsB,YAAtB,EAAoCF,QAApC,CAAtD,EAAqGa,MAArG,CAA4GC,KAA5G,CAAkH,KAAlH;EACA,CAJC,CAAF;EAMAV,EAAE,CAAC,2CAAD,EAA8C,YAAM;IACrD,IAAIF,WAAJ,CAAgB,GAAhB,EAAqB,YAArB,EAAmCF,QAAnC,EAA6CuB,eAA7C,GAA+DV,MAA/D,CAAsEC,KAAtE,CAA4E,KAA5E;IACA,IAAIZ,WAAJ,CAAgB,KAAhB,EAAuB,WAAvB,EAAoCF,QAApC,EAA8CuB,eAA9C,GAAgEV,MAAhE,CAAuEC,KAAvE,CAA6E,IAA7E;EACA,CAHC,CAAF;EAKAV,EAAE,CAAC,gCAAD,EAAmC,YAAM;IAC1C,IAAMC,WAAW,GAAG,IAAIH,WAAJ,CAAgB,GAAhB,EAAqB,YAArB,EAAmCF,QAAnC,CAApB;IACAK,WAAW,CAACC,MAAZ,CAAmB,MAAnB;IACAD,WAAW,CAACa,GAAZ,CAAgBL,MAAhB,CAAuBC,KAAvB,CAA6B,MAA7B;IACAT,WAAW,CAACW,cAAZ,GAA6BH,MAA7B,CAAoCC,KAApC,CAA0C,0BAA1C;EACA,CALC,CAAF;EAOAV,EAAE,CAAC,kCAAD,EAAqC,YAAM;IACxC;IACA;IACA;IACA;IAEJ,IAAIC,WAAW,GAAG,IAAIH,WAAJ,CAAgB,KAAhB,EAAuB,QAAvB,EAAiCF,QAAjC,CAAlB;IACAO,MAAM,CAACF,WAAW,CAACG,OAAb,CAAN,CAA4BC,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAN,WAAW,CAACmB,oBAAZ,GAAmCX,MAAnC,CAA0CY,IAA1C,CAA+CX,KAA/C,CAAqD,EAArD;IAEAT,WAAW,GAAG,IAAIH,WAAJ,CAAgB,KAAhB,EAAuB,SAAvB,EAAkCF,QAAlC,CAAd;IACAO,MAAM,CAACF,WAAW,CAACG,OAAb,CAAN,CAA4BC,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAN,WAAW,CAACmB,oBAAZ,GAAmCX,MAAnC,CAA0CY,IAA1C,CAA+CX,KAA/C,CAAqD,CAAC,IAAD,EAAO,IAAP,CAArD;IAEAT,WAAW,GAAG,IAAIH,WAAJ,CAAgB,KAAhB,EAAuB,UAAvB,EAAmCF,QAAnC,CAAd;IACAO,MAAM,CAACF,WAAW,CAACG,OAAb,CAAN,CAA4BC,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAN,WAAW,CAACmB,oBAAZ,GAAmCX,MAAnC,CAA0CY,IAA1C,CAA+CX,KAA/C,CAAqD,CAAC,IAAD,CAArD;IAEAT,WAAW,GAAG,IAAIH,WAAJ,CAAgB,KAAhB,EAAuB,WAAvB,EAAoCF,QAApC,CAAd;IACAO,MAAM,CAACF,WAAW,CAACG,OAAb,CAAN,CAA4BC,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAN,WAAW,CAACmB,oBAAZ,GAAmCX,MAAnC,CAA0CY,IAA1C,CAA+CX,KAA/C,CAAqD,EAArD;EACA,CArBC,CAAF;EAuBAV,EAAE,CAAC,uDAAD,EAA0D,YAAM;IACjE,IAAMC,WAAW,GAAG,IAAIH,WAAJ,CAAgB,GAAhB,EAAqB,YAArB,EAAmCF,QAAnC,CAApB;IACAO,MAAM,CAACF,WAAW,CAACG,OAAb,CAAN,CAA4BC,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAN,WAAW,CAACmB,oBAAZ,GAAmCE,OAAnC,CAA2C,IAA3C,EAAiDb,MAAjD,CAAwDC,KAAxD,CAA8D,CAA9D;IACAT,WAAW,CAACmB,oBAAZ,GAAmCG,MAAnC,CAA0Cd,MAA1C,CAAiDC,KAAjD,CAAuD,EAAvD;EACA,CALC,CAAF,CAnF6B,CA0F7B;EACA;EACA;EACA;EACA;;EAEAV,EAAE,CAAC,+FAAD,EAAkG,YAAM;IACzG,IAAMC,WAAW,GAAG,IAAIH,WAAJ,CAAgB,GAAhB,EAAqB,KAArB,EAA4BF,QAA5B,CAApB;IACAO,MAAM,CAACF,WAAW,CAACG,OAAb,CAAN,CAA4BC,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAN,WAAW,CAACmB,oBAAZ,GAAmCX,MAAnC,CAA0CY,IAA1C,CAA+CX,KAA/C,CAAqD,EAArD;EACA,CAJC,CAAF;EAMAV,EAAE,CAAC,0DAAD,EAA6D,YAAM;IACpE,IAAMC,WAAW,GAAG,IAAIH,WAAJ,CAAgB,IAAhB,EAAsB,YAAtB,EAAoCF,QAApC,CAApB;IACAK,WAAW,CAACG,OAAZ,CAAoBK,MAApB,CAA2BC,KAA3B,CAAiC,IAAjC;IACAT,WAAW,CAACmB,oBAAZ,GAAmCX,MAAnC,CAA0CY,IAA1C,CAA+CX,KAA/C,CAAqD,CAAC,IAAD,CAArD;EACA,CAJC,CAAF;EAMAV,EAAE,CAAC,2EAAD,EAA8E,YAAM;IACrF,IAAMC,WAAW,GAAG,IAAIH,WAAJ,CAAgB,KAAhB,EAAuB,KAAvB,EAA8BF,QAA9B,CAApB;IACAO,MAAM,CAACF,WAAW,CAACG,OAAb,CAAN,CAA4BC,EAA5B,CAA+BC,EAA/B,CAAkCC,SAAlC;IACAN,WAAW,CAACmB,oBAAZ,GAAmCX,MAAnC,CAA0CY,IAA1C,CAA+CX,KAA/C,CAAqD,EAArD;EACA,CAJC,CAAF,CA5G6B,CAkH7B;EACA;EACA;EACA;EACA;EACA;EACA;AACA,CAzHO,CAAR"}