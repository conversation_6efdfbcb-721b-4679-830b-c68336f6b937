// 插件授权标识文件
// 此文件由系统自动生成和管理，请勿手动修改
// 文件存在表示用户已通过认证且插件功能已授权

(function() {
    'use strict';

    // 检查是否已经初始化过
    if (window.PLUGIN_AUTH_INITIALIZED) {
        return;
    }

    // 标记已初始化
    window.PLUGIN_AUTH_INITIALIZED = true;

    // 授权状态标记（默认值，将由background script更新）
    if (typeof window.PLUGIN_AUTHORIZED === 'undefined') {
        window.PLUGIN_AUTHORIZED = false;
    }

    // 授权信息（将由background script动态更新）
    if (typeof window.PLUGIN_USER_INFO === 'undefined') {
        window.PLUGIN_USER_INFO = {
            username: "未知用户",
            remaining_days: 0,
            status: "unknown",
            authorized_at: new Date().toISOString()
        };
    }

    // 授权验证函数
    window.verifyPluginAuth = function() {
        try {
            // 检查基本授权状态
            if (window.PLUGIN_AUTHORIZED !== true || !window.PLUGIN_USER_INFO) {
                return false;
            }

            // 检查用户信息完整性
            const userInfo = window.PLUGIN_USER_INFO;
            if (!userInfo.username || userInfo.remaining_days <= 0 || userInfo.status !== 'active') {
                return false;
            }

            // 检查授权时间（24小时内有效）
            const authorizedAt = new Date(userInfo.authorized_at);
            const now = new Date();
            const hoursDiff = (now - authorizedAt) / (1000 * 60 * 60);

            if (hoursDiff > 24) {
                console.log('⏰ 授权已过期，需要重新验证');
                return false;
            }

            return true;
        } catch (error) {
            console.error('❌ 授权验证失败:', error);
            return false;
        }
    };

    // 获取授权状态信息
    window.getPluginAuthInfo = function() {
        return {
            authorized: window.PLUGIN_AUTHORIZED,
            userInfo: window.PLUGIN_USER_INFO,
            isValid: window.verifyPluginAuth()
        };
    };

    console.log('🔓 插件授权文件已加载');

    // 如果已授权，显示用户信息
    if (window.verifyPluginAuth()) {
        console.log('✅ 插件已授权，用户:', window.PLUGIN_USER_INFO.username, '剩余天数:', window.PLUGIN_USER_INFO.remaining_days);
    } else {
        console.log('🔒 插件未授权或授权已过期');
    }
})();
