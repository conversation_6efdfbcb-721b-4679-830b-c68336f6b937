// 插件授权标识文件
// 此文件由系统自动生成和管理，请勿手动修改
// 文件存在表示用户已通过认证且插件功能已授权

// 授权状态标记
window.PLUGIN_AUTHORIZED = true;

// 授权信息（将由background script动态更新）
window.PLUGIN_USER_INFO = {
    username: "未知用户",
    remaining_days: 0,
    status: "unknown",
    authorized_at: new Date().toISOString()
};

// 授权验证函数
window.verifyPluginAuth = function() {
    return window.PLUGIN_AUTHORIZED === true && window.PLUGIN_USER_INFO;
};

console.log('🔓 插件授权文件已加载');
