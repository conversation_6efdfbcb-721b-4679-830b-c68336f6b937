<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扩展快速测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-item {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #007cba;
        }
        .result {
            font-family: monospace;
            background: #f8f8f8;
            padding: 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <h1>🔧 扩展快速测试</h1>
    
    <div class="test-item">
        <h3>📍 地理位置测试</h3>
        <button onclick="testGeolocation()">测试地理位置</button>
        <div id="geo-result" class="result">等待测试...</div>
    </div>
    
    <div class="test-item">
        <h3>🕐 时区测试</h3>
        <button onclick="testTimezone()">测试时区</button>
        <div id="timezone-result" class="result">等待测试...</div>
    </div>
    
    <div class="test-item">
        <h3>🔍 扩展检测</h3>
        <button onclick="testExtension()">检测扩展</button>
        <div id="extension-result" class="result">等待测试...</div>
    </div>
    
    <div class="test-item">
        <h3>📝 控制台日志</h3>
        <div id="console-output" class="result">
            请打开浏览器控制台 (F12) 查看扩展日志
        </div>
    </div>

    <script>
        function testGeolocation() {
            const result = document.getElementById('geo-result');
            result.innerHTML = '🔄 正在获取地理位置...';
            
            if (!navigator.geolocation) {
                result.innerHTML = '<span class="error">❌ 地理位置API不可用</span>';
                return;
            }
            
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;
                    const accuracy = position.coords.accuracy;
                    
                    result.innerHTML = `
                        <div>📍 纬度: ${lat}</div>
                        <div>📍 经度: ${lng}</div>
                        <div>📏 精度: ${accuracy}米</div>
                        <div class="${isUSLocation(lat, lng) ? 'success' : 'error'}">
                            ${isUSLocation(lat, lng) ? '✅ 位置在美国范围内' : '❌ 位置不在美国范围内'}
                        </div>
                    `;
                },
                (error) => {
                    result.innerHTML = `<span class="error">❌ 地理位置错误: ${error.message}</span>`;
                }
            );
        }
        
        function testTimezone() {
            const result = document.getElementById('timezone-result');
            
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            const offset = new Date().getTimezoneOffset();
            const dateString = new Date().toString();
            
            const isUSTimezone = [
                'America/New_York', 'America/Chicago', 'America/Denver', 
                'America/Los_Angeles', 'America/Phoenix', 'America/Anchorage',
                'Pacific/Honolulu'
            ].includes(timezone);
            
            result.innerHTML = `
                <div>🕐 时区: ${timezone}</div>
                <div>⏰ 偏移: ${offset} 分钟</div>
                <div>📅 时间: ${dateString}</div>
                <div class="${isUSTimezone ? 'success' : 'error'}">
                    ${isUSTimezone ? '✅ 时区是美国时区' : '❌ 时区不是美国时区'}
                </div>
            `;
        }
        
        function testExtension() {
            const result = document.getElementById('extension-result');
            let status = [];
            
            // 检查Date API是否被修改
            const dateModified = Date.prototype.getTimezoneOffset.toString().includes('dynamicOffset');
            status.push(`Date API: ${dateModified ? '✅ 已修改' : '❌ 未修改'}`);
            
            // 检查地理位置API是否被修改
            const geoModified = navigator.geolocation && 
                navigator.geolocation.getCurrentPosition.toString().includes('LOCATION_DATA');
            status.push(`地理位置API: ${geoModified ? '✅ 已修改' : '❌ 未修改'}`);
            
            // 检查localStorage设置
            const settings = localStorage.getItem('globalTimezoneSpoofer_selectedCity');
            status.push(`扩展设置: ${settings ? '✅ 已保存' : '❌ 未保存'}`);
            
            // 检查Intl.DateTimeFormat是否被修改
            const intlModified = Intl.DateTimeFormat.toString().includes('LOCATION_DATA');
            status.push(`Intl API: ${intlModified ? '✅ 已修改' : '❌ 未修改'}`);
            
            result.innerHTML = status.join('<br>');
        }
        
        function isUSLocation(lat, lng) {
            // 美国大陆范围
            return lat >= 24.396308 && lat <= 49.384358 && lng >= -125.0 && lng <= -66.93457;
        }
        
        // 页面加载时自动运行测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                testExtension();
                testTimezone();
            }, 1000);
        });
        
        // 拦截控制台输出
        const originalLog = console.log;
        const consoleOutput = document.getElementById('console-output');
        let logs = [];
        
        console.log = function(...args) {
            const message = args.join(' ');
            logs.push(message);
            
            // 只显示扩展相关的日志
            if (message.includes('🌍') || message.includes('Global Timezone') || 
                message.includes('Spoofer') || message.includes('🔧')) {
                consoleOutput.innerHTML = logs.filter(log => 
                    log.includes('🌍') || log.includes('Global Timezone') || 
                    log.includes('Spoofer') || log.includes('🔧')
                ).slice(-10).join('<br>');
            }
            
            return originalLog.apply(console, args);
        };
    </script>
</body>
</html>
