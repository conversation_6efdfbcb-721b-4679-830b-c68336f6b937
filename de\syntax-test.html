<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Syntax Test - Global Timezone Spoofer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f0f8ff;
        }
        .status {
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-size: 18px;
            text-align: center;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-results {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
            border-left: 4px solid #007acc;
        }
    </style>
</head>
<body>
    <h1>🧪 Global Timezone Spoofer - Syntax Test</h1>
    
    <div id="syntax-status" class="status info">
        ⏳ Testing extension syntax...
    </div>

    <div id="test-results"></div>

    <script>
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('syntax-status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
        }

        function addTestResult(content) {
            const resultsDiv = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = 'test-results';
            div.innerHTML = content;
            resultsDiv.appendChild(div);
        }

        // Test if the extension loaded without syntax errors
        setTimeout(() => {
            try {
                // Test basic functionality
                const now = new Date();
                const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
                
                addTestResult(`✅ Extension loaded successfully!`);
                addTestResult(`📅 Current Date: ${now.toString()}`);
                addTestResult(`🌍 Detected Timezone: ${timezone}`);
                addTestResult(`⏰ Timezone Offset: ${now.getTimezoneOffset()} minutes`);
                addTestResult(`🗣️ Language: ${navigator.language}`);
                addTestResult(`🗣️ Languages: ${navigator.languages.join(', ')}`);
                
                // Test geolocation
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(
                        (position) => {
                            addTestResult(`📍 Location: ${position.coords.latitude}, ${position.coords.longitude}`);
                            addTestResult(`🎯 Accuracy: ${position.coords.accuracy} meters`);
                        },
                        (error) => {
                            addTestResult(`❌ Geolocation Error: ${error.message}`);
                        },
                        { timeout: 5000 }
                    );
                } else {
                    addTestResult(`❌ Geolocation not available`);
                }
                
                updateStatus('✅ Extension syntax test passed! No errors detected.', 'success');
                
            } catch (error) {
                updateStatus(`❌ Extension syntax error detected: ${error.message}`, 'error');
                addTestResult(`❌ Error Details: ${error.stack}`);
            }
        }, 1000);

        // Check console for any errors
        const originalConsoleError = console.error;
        let hasErrors = false;
        
        console.error = function(...args) {
            hasErrors = true;
            addTestResult(`❌ Console Error: ${args.join(' ')}`);
            originalConsoleError.apply(console, args);
        };

        // Final check after 3 seconds
        setTimeout(() => {
            if (!hasErrors) {
                updateStatus('🎉 All tests passed! Extension is working correctly.', 'success');
            } else {
                updateStatus('⚠️ Some errors were detected. Check the results below.', 'error');
            }
        }, 3000);
    </script>
</body>
</html>
