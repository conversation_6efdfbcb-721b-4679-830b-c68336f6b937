<?php
// 调试版本的激活API
error_reporting(E_ALL);
ini_set('display_errors', 1);

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-Custom-Header');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit();
}

try {
    // 获取输入数据
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        echo json_encode(['success' => false, 'message' => '无效的JSON数据']);
        exit();
    }

    // 验证必需字段
    $userId = (int)($input['user_id'] ?? 0);
    $activationCode = trim($input['activation_code'] ?? '');

    if ($userId <= 0 || empty($activationCode)) {
        echo json_encode(['success' => false, 'message' => '用户ID和激活码不能为空']);
        exit();
    }

    // 激活码格式验证已移除，由数据库验证决定

    // 数据库连接
    require_once 'config.php';
    $pdo = getDBConnection();

    if (!$pdo) {
        echo json_encode(['success' => false, 'message' => '数据库连接失败']);
        exit();
    }

    // 连接激活码数据库验证激活码
    $activationPdo = getActivationDBConnection();
    if (!$activationPdo) {
        echo json_encode(['success' => false, 'message' => '激活码验证服务暂时不可用']);
        exit();
    }

    // 查询激活码
    $stmt = $activationPdo->prepare("
        SELECT id, code, use_times, is_used, paid, remark
        FROM activation_codes
        WHERE code = ? AND remark = 'timezone' AND paid = 1
    ");
    $stmt->execute([$activationCode]);
    $codeData = $stmt->fetch();

    if (!$codeData) {
        echo json_encode(['success' => false, 'message' => '您已经使用过此激活码或激活码不存在']);
        exit();
    }

    // 检查激活码是否已使用
    if ($codeData['is_used'] == 1) {
        echo json_encode(['success' => false, 'message' => '您已经使用过此激活码或激活码不存在']);
        exit();
    }

    $daysToAdd = (int)$codeData['use_times'];

    // 开始事务
    $pdo->beginTransaction();

    // 验证用户是否存在
    $stmt = $pdo->prepare("SELECT id, username, expires_at, status, used_activation_codes FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();

    if (!$user) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '用户不存在']);
        exit();
    }

    // 检查激活码是否已被该用户使用过
    $usedCodes = $user['used_activation_codes'] ? explode(',', $user['used_activation_codes']) : [];
    if (in_array($activationCode, $usedCodes)) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '该激活码已被使用过']);
        exit();
    }

    // 计算新的到期时间
    $currentTime = new DateTime();
    $currentExpiry = $user['expires_at'] ? new DateTime($user['expires_at']) : null;

    // 如果当前到期时间晚于现在，在原基础上延长；否则从现在开始计算
    $baseTime = ($currentExpiry && $currentExpiry > $currentTime) ? $currentExpiry : $currentTime;
    $newExpiry = clone $baseTime;
    $newExpiry->add(new DateInterval("P{$daysToAdd}D"));
    $newExpiryDate = $newExpiry->format('Y-m-d H:i:s');

    // 更新已使用的激活码列表
    $newUsedCodes = $usedCodes;
    $newUsedCodes[] = $activationCode;
    $usedCodesString = implode(',', $newUsedCodes);

    // 更新用户到期时间和已使用激活码
    $stmt = $pdo->prepare("
        UPDATE users
        SET expires_at = ?, status = 'active', used_activation_codes = ?, updated_at = NOW()
        WHERE id = ?
    ");
    $stmt->execute([$newExpiryDate, $usedCodesString, $userId]);

    // 标记激活码为已使用
    $stmt = $activationPdo->prepare("
        UPDATE activation_codes
        SET is_used = 1
        WHERE id = ?
    ");
    $stmt->execute([$codeData['id']]);

    // 计算新的剩余天数用于显示
    $currentTime = new DateTime();
    $expiryTime = new DateTime($newExpiryDate);
    $newRemainingDays = 0;
    if ($currentTime < $expiryTime) {
        $diff = $currentTime->diff($expiryTime);
        $newRemainingDays = $diff->days;
    }

    // 提交事务
    $pdo->commit();

    // 返回成功响应
    echo json_encode([
        'success' => true,
        'message' => '激活成功',
        'data' => [
            'user_id' => $userId,
            'username' => $user['username'],
            'days_added' => $daysToAdd,
            'expires_at' => $newExpiryDate,
            'remaining_days' => $newRemainingDays,
            'status' => 'active'
        ]
    ]);

} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    echo json_encode([
        'success' => false,
        'message' => '激活过程中发生错误: ' . $e->getMessage(),
        'error_details' => $e->getTraceAsString()
    ]);
}
?>
