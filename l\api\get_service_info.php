<?php
// 获取客服联系信息API
require_once 'config.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $pdo = getDBConnection();
    if (!$pdo) {
        sendResponse(false, '数据库连接失败');
    }

    // 获取所有客服配置信息
    $serviceKeys = ['service_qq', 'service_wechat', 'service_phone', 'service_email', 'service_hours'];
    $serviceInfo = [];
    
    foreach ($serviceKeys as $key) {
        $value = getSystemConfig($pdo, $key);
        if ($value) {
            $serviceInfo[$key] = $value;
        }
    }
    
    // 记录访问日志
    logServiceInfoAccess($pdo);
    
    sendResponse(true, '获取客服信息成功', $serviceInfo);

} catch (Exception $e) {
    error_log("获取客服信息失败: " . $e->getMessage());
    sendResponse(false, '获取客服信息失败：' . $e->getMessage());
}

/**
 * 记录客服信息访问日志
 */
function logServiceInfoAccess($pdo) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO access_logs (
                log_type, 
                ip_address, 
                user_agent, 
                request_data, 
                created_at
            ) VALUES (?, ?, ?, ?, NOW())
        ");
        
        $logData = json_encode([
            'action' => 'get_service_info',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
        
        $stmt->execute([
            'service_info',
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            $logData
        ]);
    } catch (Exception $e) {
        // 日志记录失败不影响主要功能
        error_log("记录客服信息访问日志失败: " . $e->getMessage());
    }
}
?>
