{"version": 3, "file": "util.js", "names": ["limit", "lower", "upper", "TypeError", "trimAfterFirstMatch", "regexp", "string", "index", "search", "slice", "startsWith", "substring", "indexOf", "endsWith", "length"], "sources": ["../../source/findNumbers/util.js"], "sourcesContent": ["/** Returns a regular expression quantifier with an upper and lower limit. */\r\nexport function limit(lower, upper)\r\n{\r\n\tif ((lower < 0) || (upper <= 0) || (upper < lower)) {\r\n\t\tthrow new TypeError()\r\n\t}\r\n\treturn `{${lower},${upper}}`\r\n}\r\n\r\n/**\r\n * Trims away any characters after the first match of {@code pattern} in {@code candidate},\r\n * returning the trimmed version.\r\n */\r\nexport function trimAfterFirstMatch(regexp, string)\r\n{\r\n\tconst index = string.search(regexp)\r\n\r\n\tif (index >= 0) {\r\n\t\treturn string.slice(0, index)\r\n\t}\r\n\r\n\treturn string\r\n}\r\n\r\nexport function startsWith(string, substring)\r\n{\r\n\treturn string.indexOf(substring) === 0\r\n}\r\n\r\nexport function endsWith(string, substring)\r\n{\r\n\treturn string.indexOf(substring, string.length - substring.length) === string.length - substring.length\r\n}\r\n"], "mappings": "AAAA;AACA,OAAO,SAASA,KAAT,CAAeC,KAAf,EAAsBC,KAAtB,EACP;EACC,IAAKD,KAAK,GAAG,CAAT,IAAgBC,KAAK,IAAI,CAAzB,IAAgCA,KAAK,GAAGD,KAA5C,EAAoD;IACnD,MAAM,IAAIE,SAAJ,EAAN;EACA;;EACD,kBAAWF,KAAX,cAAoBC,KAApB;AACA;AAED;AACA;AACA;AACA;;AACA,OAAO,SAASE,mBAAT,CAA6BC,MAA7B,EAAqCC,MAArC,EACP;EACC,IAAMC,KAAK,GAAGD,MAAM,CAACE,MAAP,CAAcH,MAAd,CAAd;;EAEA,IAAIE,KAAK,IAAI,CAAb,EAAgB;IACf,OAAOD,MAAM,CAACG,KAAP,CAAa,CAAb,EAAgBF,KAAhB,CAAP;EACA;;EAED,OAAOD,MAAP;AACA;AAED,OAAO,SAASI,UAAT,CAAoBJ,MAApB,EAA4BK,SAA5B,EACP;EACC,OAAOL,MAAM,CAACM,OAAP,CAAeD,SAAf,MAA8B,CAArC;AACA;AAED,OAAO,SAASE,QAAT,CAAkBP,MAAlB,EAA0BK,SAA1B,EACP;EACC,OAAOL,MAAM,CAACM,OAAP,CAAeD,SAAf,EAA0BL,MAAM,CAACQ,MAAP,GAAgBH,SAAS,CAACG,MAApD,MAAgER,MAAM,CAACQ,MAAP,GAAgBH,SAAS,CAACG,MAAjG;AACA"}