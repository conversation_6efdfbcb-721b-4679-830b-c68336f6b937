
// Background script for Global Timezone & Location Spoofer
chrome.runtime.onInstalled.addListener(() => {
    console.log('🌍 Global Timezone & Location Spoofer v2.9.7 installed');
    console.log('🛡️ 多地区支持版：美国、台湾、日本、新加坡时区和位置伪装');
    console.log('👨‍💻 作者：小鱼游水 | 🌐 网址：https://xoxome.online');

    // 初始化默认设置
    chrome.storage.sync.get(['selectedRegion'], (result) => {
        if (!result.selectedRegion) {
            console.log('🎯 Setting default region to US');
            chrome.storage.sync.set({
                selectedRegion: 'US',
                lastUpdate: Date.now()
            });
        }
    });

    // 检查用户认证状态并生成授权文件
    checkAuthAndGenerateFile();
});

// 检查用户认证状态并生成授权文件
async function checkAuthAndGenerateFile() {
    try {
        // 获取用户数据
        const userData = await getStoredUserData();
        if (!userData || !userData.user_id) {
            console.log('🔒 用户未登录，删除授权文件');
            await removeAuthFile();
            return;
        }

        // 验证用户状态
        const API_BASE = 'http://103.96.75.196/api';
        const response = await fetch(`${API_BASE}/user_status.php?user_id=${userData.user_id}`);
        const result = await response.json();

        if (result.success && result.data.remaining_days > 0 && result.data.status === 'active') {
            console.log('✅ 用户认证通过，生成授权文件');
            await generateAuthFile(result.data);
        } else {
            console.log('🔒 用户认证失败或已过期，删除授权文件');
            await removeAuthFile();
        }
    } catch (error) {
        console.error('❌ 认证检查失败:', error);
        await removeAuthFile();
    }
}

// 生成授权文件
async function generateAuthFile(userData) {
    try {
        // 更新 ind.js 文件内容
        const authContent = `// 插件授权文件 - 自动生成，请勿手动修改
// Generated at: ${new Date().toISOString()}
// User: ${userData.username}
// Remaining days: ${userData.remaining_days}
// Status: ${userData.status}

window.PLUGIN_AUTHORIZED = true;
window.PLUGIN_USER_INFO = {
    username: "${userData.username}",
    remaining_days: ${userData.remaining_days},
    status: "${userData.status}",
    authorized_at: "${new Date().toISOString()}"
};

// 授权验证函数
window.verifyPluginAuth = function() {
    return window.PLUGIN_AUTHORIZED === true && window.PLUGIN_USER_INFO;
};

console.log('🔓 插件已授权，用户:', "${userData.username}", '剩余天数:', ${userData.remaining_days});
`;

        // 使用 chrome.scripting API 在所有标签页中注入授权信息
        const tabs = await chrome.tabs.query({});
        for (const tab of tabs) {
            try {
                await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    func: (userInfo) => {
                        // 创建授权标记
                        window.PLUGIN_AUTHORIZED = true;
                        window.PLUGIN_USER_INFO = userInfo;

                        // 在localStorage中也保存授权信息
                        localStorage.setItem('plugin_auth_status', 'authorized');
                        localStorage.setItem('plugin_user_info', JSON.stringify(userInfo));

                        // 创建授权验证函数
                        window.verifyPluginAuth = function() {
                            return window.PLUGIN_AUTHORIZED === true && window.PLUGIN_USER_INFO;
                        };

                        console.log('🔓 插件授权已注入，用户:', userInfo.username);
                    },
                    args: [{
                        username: userData.username,
                        remaining_days: userData.remaining_days,
                        status: userData.status,
                        authorized_at: new Date().toISOString()
                    }]
                });
            } catch (e) {
                // 忽略无法访问的标签页
            }
        }

        // 保存授权状态到扩展存储
        await chrome.storage.local.set({
            'plugin_authorized': true,
            'auth_generated_at': Date.now(),
            'auth_user_info': userData,
            'ind_file_status': 'active' // 标记ind.js文件状态
        });

        console.log('✅ 授权文件生成成功，用户:', userData.username);
    } catch (error) {
        console.error('❌ 生成授权文件失败:', error);
    }
}

// 删除授权文件
async function removeAuthFile() {
    try {
        // 从所有标签页中移除授权信息
        const tabs = await chrome.tabs.query({});
        for (const tab of tabs) {
            try {
                await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    func: () => {
                        // 移除授权标记
                        window.PLUGIN_AUTHORIZED = false;
                        delete window.PLUGIN_USER_INFO;
                        delete window.verifyPluginAuth;

                        // 从localStorage中移除授权信息
                        localStorage.removeItem('plugin_auth_status');
                        localStorage.removeItem('plugin_user_info');

                        console.log('🔒 插件授权已移除');
                    }
                });
            } catch (e) {
                // 忽略无法访问的标签页
            }
        }

        // 从扩展存储中移除授权状态
        await chrome.storage.local.remove([
            'plugin_authorized',
            'auth_generated_at',
            'auth_user_info',
            'ind_file_status'
        ]);

        console.log('🔒 授权文件已删除');
    } catch (error) {
        console.error('❌ 删除授权文件失败:', error);
    }
}

// 获取存储的用户数据
async function getStoredUserData() {
    return new Promise((resolve) => {
        chrome.storage.local.get(['user_data'], (result) => {
            resolve(result.user_data || null);
        });
    });
}

// 监听用户数据变化
chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'local' && changes.user_data) {
        console.log('👤 用户数据发生变化，重新检查授权');
        checkAuthAndGenerateFile();
    }
});

// 定期检查用户授权状态（每30分钟检查一次）
setInterval(() => {
    console.log('🔄 定期检查用户授权状态');
    checkAuthAndGenerateFile();
}, 30 * 60 * 1000);

// 监听来自popup和auth页面的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'generateAuthFile' && request.userData) {
        console.log('📨 收到生成授权文件请求');
        generateAuthFile(request.userData);
        sendResponse({ success: true });
    } else if (request.action === 'removeAuthFile') {
        console.log('📨 收到删除授权文件请求');
        removeAuthFile();
        sendResponse({ success: true });
    }
    return true; // 保持消息通道开放
});

// 监听存储变化，同步到所有标签页
chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'sync' && changes.selectedCity) {
        console.log('🔄 Region settings changed, syncing to all tabs');

        // 获取所有标签页并注入localStorage更新
        chrome.tabs.query({}, (tabs) => {
            const storageKey = 'globalTimezoneSpoofer_selectedCity';
            const cityData = changes.selectedCity.newValue;

            tabs.forEach(tab => {
                try {
                    chrome.scripting.executeScript({
                        target: { tabId: tab.id },
                        func: (key, data) => {
                            localStorage.setItem(key, JSON.stringify(data));
                            console.log('🔄 Updated localStorage with new region settings');
                        },
                        args: [storageKey, cityData]
                    }).catch(() => {
                        // 忽略无法访问的标签页
                    });
                } catch (e) {
                    // 忽略错误
                }
            });
        });
    }
});
