
// Background script for Global Timezone & Location Spoofer
chrome.runtime.onInstalled.addListener(() => {
    console.log('🌍 Global Timezone & Location Spoofer v2.9.7 installed');
    console.log('🛡️ 多地区支持版：美国、台湾、日本、新加坡时区和位置伪装');
    console.log('👨‍💻 作者：小鱼游水 | 🌐 网址：https://xoxome.online');

    // 初始化默认设置
    chrome.storage.sync.get(['selectedRegion'], (result) => {
        if (!result.selectedRegion) {
            console.log('🎯 Setting default region to US');
            chrome.storage.sync.set({
                selectedRegion: 'US',
                lastUpdate: Date.now()
            });
        }
    });
});

// 监听存储变化，同步到所有标签页
chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'sync' && changes.selectedCity) {
        console.log('🔄 Region settings changed, syncing to all tabs');

        // 获取所有标签页并注入localStorage更新
        chrome.tabs.query({}, (tabs) => {
            const storageKey = 'globalTimezoneSpoofer_selectedCity';
            const cityData = changes.selectedCity.newValue;

            tabs.forEach(tab => {
                try {
                    chrome.scripting.executeScript({
                        target: { tabId: tab.id },
                        func: (key, data) => {
                            localStorage.setItem(key, JSON.stringify(data));
                            console.log('🔄 Updated localStorage with new region settings');
                        },
                        args: [storageKey, cityData]
                    }).catch(() => {
                        // 忽略无法访问的标签页
                    });
                } catch (e) {
                    // 忽略错误
                }
            });
        });
    }
});
