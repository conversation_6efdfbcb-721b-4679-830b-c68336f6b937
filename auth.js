// 认证系统 JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // API基础URL - 使用服务器IP
    const API_BASE = 'http://*************/api';

    // DOM元素
    const authTabs = document.querySelectorAll('.auth-tab');
    const authForms = document.querySelectorAll('.auth-form');
    const loginForm = document.getElementById('login-form');
    const registerForm = document.getElementById('register-form');
    const activationSection = document.getElementById('activation-section');
    const userInfo = document.getElementById('user-info');
    const message = document.getElementById('message');
    const loading = document.getElementById('loading');
    const purchaseLink = document.getElementById('purchase-link');

    // 按钮元素
    const loginBtn = document.getElementById('login-btn');
    const registerBtn = document.getElementById('register-btn');
    const activateBtn = document.getElementById('activate-btn');

    // 输入框元素
    const loginUsername = document.getElementById('login-username');
    const loginPassword = document.getElementById('login-password');
    const registerUsername = document.getElementById('register-username');
    const registerPassword = document.getElementById('register-password');
    const registerConfirm = document.getElementById('register-confirm');
    const activationCode = document.getElementById('activation-code');

    // 用户信息显示元素
    const usernameDisplay = document.getElementById('username-display');
    const remainingDays = document.getElementById('remaining-days');
    const accountStatus = document.getElementById('account-status');

    // 过期提示元素
    const expiredNotice = document.getElementById('expired-notice');
    const purchaseButton = document.getElementById('purchase-button');
    const authContainer = document.querySelector('.auth-container');

    // 当前用户数据
    let currentUser = null;

    // 初始化
    init();

    function init() {
        // 绑定事件
        bindEvents();

        // 检查是否已登录
        checkLoginStatus();
    }

    function bindEvents() {
        // 标签页切换
        authTabs.forEach(tab => {
            tab.addEventListener('click', () => switchTab(tab.dataset.tab));
        });

        // 登录按钮
        loginBtn.addEventListener('click', handleLogin);

        // 注册按钮
        registerBtn.addEventListener('click', handleRegister);

        // 激活按钮
        activateBtn.addEventListener('click', handleActivation);

        // 回车键提交
        loginPassword.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') handleLogin();
        });

        registerConfirm.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') handleRegister();
        });

        activationCode.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') handleActivation();
        });

        // 购买链接点击
        purchaseLink.addEventListener('click', (e) => {
            e.preventDefault();
            if (purchaseLink.href && purchaseLink.href !== '#') {
                chrome.tabs.create({ url: purchaseLink.href });
            }
        });
    }

    // 切换标签页
    function switchTab(tabName) {
        // 更新标签页状态
        authTabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabName);
        });

        // 更新表单显示
        authForms.forEach(form => {
            form.classList.toggle('active', form.id === `${tabName}-form`);
        });

        // 清除消息
        hideMessage();
    }

    // 移除设备指纹生成 - 不再限制设备

    // 检查登录状态
    async function checkLoginStatus() {
        const userData = await getStoredUserData();
        if (userData && userData.user_id) {
            try {
                const response = await apiRequest(`${API_BASE}/user_status.php?user_id=${userData.user_id}`);
                if (response.success) {
                    currentUser = response.data;
                    if (response.data.remaining_days > 0 && response.data.status === 'active') {
                        showUserInterface();
                    } else {
                        // 用户过期，显示过期界面
                        showExpiredUserInterface(response.data);
                    }
                } else {
                    clearStoredUserData();
                }
            } catch (error) {
                console.error('检查登录状态失败:', error);
                clearStoredUserData();
            }
        }
    }

    // 处理登录
    async function handleLogin() {
        const username = loginUsername.value.trim();
        const password = loginPassword.value.trim();

        if (!username || !password) {
            showMessage('请填写用户名和密码', 'error');
            return;
        }

        setLoading(true);

        try {
            const response = await apiRequest(`${API_BASE}/login.php`, {
                method: 'POST',
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            });

            if (response.success) {
                currentUser = response.data;
                await storeUserData(currentUser);
                showMessage('登录成功！', 'success');
                setTimeout(() => {
                    if (currentUser.remaining_days > 0) {
                        // 跳转到主界面
                        window.location.href = 'popup.html';
                    } else {
                        showUserInterface();
                    }
                }, 1000);
            } else {
                console.log('登录失败响应:', response);
                showMessage(response.message, 'error');
                if (response.data && response.data.expired && response.data.user_data) {
                    // 过期用户，显示过期界面
                    console.log('显示过期用户界面，用户数据:', response.data.user_data);
                    updatePurchaseLink(response.data.purchase_link);
                    showExpiredUserInterface(response.data.user_data);
                } else {
                    console.log('未检测到过期用户数据，响应数据:', response.data);
                }
            }
        } catch (error) {
            console.error('登录失败:', error);
            showMessage('登录失败，请检查网络连接', 'error');
        } finally {
            setLoading(false);
        }
    }

    // 处理注册
    async function handleRegister() {
        const username = registerUsername.value.trim();
        const password = registerPassword.value.trim();
        const confirmPassword = registerConfirm.value.trim();

        // 验证输入
        if (!username || !password || !confirmPassword) {
            showMessage('请填写所有字段', 'error');
            return;
        }

        if (password !== confirmPassword) {
            showMessage('两次输入的密码不一致', 'error');
            return;
        }

        if (username.length < 3 || username.length > 20) {
            showMessage('用户名长度必须在3-20个字符之间', 'error');
            return;
        }

        if (!/^[a-zA-Z0-9_]+$/.test(username)) {
            showMessage('用户名只能包含字母、数字和下划线', 'error');
            return;
        }

        if (password.length < 6) {
            showMessage('密码长度至少6个字符', 'error');
            return;
        }

        setLoading(true);

        try {
            const response = await apiRequest(`${API_BASE}/register.php`, {
                method: 'POST',
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            });

            if (response.success) {
                // 检查用户状态
                if (response.data.status === 'expired' || response.data.remaining_days <= 0) {
                    // 没有试用期，直接显示过期界面
                    showMessage('注册成功！', 'success');
                    currentUser = response.data;
                    await storeUserData(currentUser);
                    setTimeout(() => {
                        showExpiredUserInterface(response.data);
                    }, 1500);
                } else {
                    // 有试用期，正常流程
                    showMessage('注册成功！请登录', 'success');
                    // 清空注册表单
                    registerUsername.value = '';
                    registerPassword.value = '';
                    registerConfirm.value = '';
                    // 切换到登录标签页
                    setTimeout(() => {
                        switchTab('login');
                        loginUsername.value = username;
                    }, 1500);
                }
            } else {
                showMessage(response.message, 'error');
            }
        } catch (error) {
            console.error('注册失败:', error);
            showMessage('注册失败，请检查网络连接', 'error');
        } finally {
            setLoading(false);
        }
    }

    // 处理激活
    async function handleActivation() {
        const code = activationCode.value.trim();

        if (!code) {
            showMessage('请输入激活码', 'error');
            return;
        }

        if (!currentUser || !currentUser.user_id) {
            showMessage('请先登录', 'error');
            return;
        }

        setLoading(true);

        try {
            const response = await apiRequest(`${API_BASE}/activate.php`, {
                method: 'POST',
                body: JSON.stringify({
                    user_id: currentUser.user_id,
                    activation_code: code
                })
            });

            if (response.success) {
                currentUser = response.data;
                await storeUserData(currentUser);
                showMessage(`激活成功！增加了${response.data.days_added}天使用时间`, 'success');
                activationCode.value = '';
                updateUserInfo();

                // 如果激活后有剩余天数，跳转到主界面
                if (currentUser.remaining_days > 0) {
                    setTimeout(() => {
                        window.location.href = 'popup.html';
                    }, 2000);
                }
            } else {
                showMessage(response.message, 'error');
            }
        } catch (error) {
            console.error('激活失败:', error);
            showMessage('激活失败，请检查网络连接', 'error');
        } finally {
            setLoading(false);
        }
    }

    // API请求函数
    async function apiRequest(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        };

        const response = await fetch(url, { ...defaultOptions, ...options });

        // 检查响应状态
        if (!response.ok && response.status !== 402) {
            // 402是过期用户的预期状态码，不应该抛出异常
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('API响应:', { status: response.status, data: result });
        return result;
    }

    // 显示用户界面
    function showUserInterface() {
        if (currentUser) {
            updateUserInfo();
            userInfo.classList.add('show');
            activationSection.classList.add('show');

            if (currentUser.purchase_link) {
                updatePurchaseLink(currentUser.purchase_link);
            }

            // 如果剩余天数大于0，显示进入主界面的按钮
            if (currentUser.remaining_days > 0) {
                const enterBtn = document.createElement('button');
                enterBtn.className = 'btn btn-primary';
                enterBtn.textContent = '🚀 进入主界面';
                enterBtn.onclick = () => window.location.href = 'popup.html';
                activationSection.appendChild(enterBtn);
            }
        }
    }

    // 更新用户信息显示
    function updateUserInfo() {
        if (currentUser) {
            usernameDisplay.textContent = currentUser.username;
            remainingDays.textContent = currentUser.remaining_days;
            accountStatus.textContent = getStatusText(currentUser.status);

            // 根据剩余天数设置颜色
            if (currentUser.remaining_days <= 0) {
                remainingDays.style.color = '#f44336';
                showExpiredNotice();
            } else if (currentUser.remaining_days <= 3) {
                remainingDays.style.color = '#FFC107';
                hideExpiredNotice();
            } else {
                remainingDays.style.color = '#4CAF50';
                hideExpiredNotice();
            }
        }
    }

    // 显示过期提示
    function showExpiredNotice() {
        const expiredNotice = document.getElementById('expired-notice');
        const purchaseButton = document.getElementById('purchase-button');

        if (expiredNotice) {
            expiredNotice.style.display = 'block';

            // 绑定购买按钮事件
            if (purchaseButton && currentUser && currentUser.purchase_link) {
                purchaseButton.onclick = () => {
                    if (currentUser.purchase_link && currentUser.purchase_link !== '#') {
                        window.open(currentUser.purchase_link, '_blank');
                    } else {
                        showMessage('购买链接暂未配置，请联系客服', 'error');
                    }
                };
            }
        }
    }

    // 隐藏过期提示
    function hideExpiredNotice() {
        const expiredNotice = document.getElementById('expired-notice');
        if (expiredNotice) {
            expiredNotice.style.display = 'none';
        }
    }

    // 获取状态文本
    function getStatusText(status) {
        const statusMap = {
            'active': '正常',
            'expired': '已过期',
            'suspended': '已暂停'
        };
        return statusMap[status] || status;
    }

    // 显示过期用户界面
    function showExpiredUserInterface(userData) {
        console.log('showExpiredUserInterface 被调用，用户数据:', userData);

        // 隐藏整个页面内容，包括所有表单和卡片
        const container = document.querySelector('.container');
        if (container) {
            container.style.display = 'none';
        }

        // 隐藏所有可能的表单元素
        const authContainer = document.querySelector('.auth-container');
        if (authContainer) {
            authContainer.style.display = 'none';
        }

        // 创建过期提示界面
        const expiredContainer = document.createElement('div');
        expiredContainer.className = 'expired-container';
        expiredContainer.innerHTML = `
            <div class="expired-content">
                <div class="expired-icon">⏰</div>
                <h2>账户已过期</h2>
                <p class="expired-message">您的试用期已结束，请购买激活码继续使用</p>

                <div class="expired-actions">
                    <button id="buyActivationCode" class="btn-primary pulse">
                        🛒 立即购买激活码
                    </button>

                    <!-- 激活码输入卡片 -->
                    <div class="activation-card">
                        <h3>✨ 输入激活码</h3>
                        <div class="form-group">
                            <input type="text" id="expired-activation-code" placeholder="请输入激活码">
                        </div>
                        <button id="expired-activate-btn" class="btn-secondary">激活</button>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .expired-container {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 20px;
                z-index: 10000;
            }
            .expired-content {
                background: rgba(255, 255, 255, 0.98);
                border-radius: 20px;
                padding: 40px;
                text-align: center;
                max-width: 450px;
                width: 100%;
                box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
                backdrop-filter: blur(10px);
            }
            .expired-icon {
                font-size: 64px;
                margin-bottom: 20px;
                animation: bounce 2s infinite;
            }
            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                40% { transform: translateY(-10px); }
                60% { transform: translateY(-5px); }
            }
            .expired-content h2 {
                color: #333;
                margin-bottom: 15px;
                font-size: 28px;
                font-weight: bold;
            }
            .expired-message {
                margin-bottom: 30px;
                color: #666;
                line-height: 1.6;
                font-size: 16px;
            }
            .expired-actions {
                display: flex;
                flex-direction: column;
                gap: 20px;
                align-items: center;
            }
            /* 按钮样式 */
            .btn-primary {
                background: linear-gradient(45deg, #FF6B6B, #FF8E53);
                color: white;
                font-weight: bold;
                padding: 15px 30px;
                border: none;
                border-radius: 25px;
                font-size: 18px;
                cursor: pointer;
                transition: all 0.3s ease;
                width: 100%;
                box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
            }
            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
            }
            .btn-primary.pulse {
                animation: pulse 2s infinite;
            }
            @keyframes pulse {
                0% { box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3); }
                50% { box-shadow: 0 4px 25px rgba(255, 107, 107, 0.6); }
                100% { box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3); }
            }

            /* 激活码卡片样式 */
            .activation-card {
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 15px;
                padding: 25px;
                margin-top: 20px;
                width: 100%;
            }
            .activation-card h3 {
                color: #333;
                margin-bottom: 15px;
                font-size: 18px;
                font-weight: bold;
            }
            .activation-card .form-group {
                margin-bottom: 15px;
            }
            .activation-card input {
                width: 100%;
                padding: 12px 15px;
                border: 1px solid #ced4da;
                border-radius: 8px;
                font-size: 16px;
                text-align: center;
                letter-spacing: 2px;
                font-family: monospace;
                transition: border-color 0.3s ease;
            }
            .activation-card input:focus {
                outline: none;
                border-color: #FF6B6B;
                box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.2);
            }
            .btn-secondary {
                background: #6c757d;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 20px;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s ease;
                width: 100%;
            }
            .btn-secondary:hover {
                background: #5a6268;
                transform: translateY(-1px);
            }

            @media (max-width: 480px) {
                .expired-content {
                    padding: 30px 20px;
                }
                .activation-card {
                    padding: 20px;
                }
            }
        `;
        document.head.appendChild(style);

        // 插入到页面body中
        document.body.appendChild(expiredContainer);

        // 绑定事件
        document.getElementById('buyActivationCode').addEventListener('click', () => {
            if (userData.purchase_link && userData.purchase_link !== '#') {
                window.open(userData.purchase_link, '_blank');
            } else {
                showMessage('购买链接暂未配置，请联系客服', 'error');
            }
        });

        // 激活码输入和激活按钮事件
        const expiredActivationCode = document.getElementById('expired-activation-code');
        const expiredActivateBtn = document.getElementById('expired-activate-btn');

        // 激活按钮点击事件
        expiredActivateBtn.addEventListener('click', async () => {
            const code = expiredActivationCode.value.trim();

            if (!code) {
                alert('请输入激活码');
                return;
            }

            // 设置按钮为加载状态
            expiredActivateBtn.disabled = true;
            expiredActivateBtn.textContent = '激活中...';

            try {
                const response = await apiRequest('http://*************/api/activate.php', {
                    method: 'POST',
                    body: JSON.stringify({
                        user_id: userData.user_id || userData.id,
                        activation_code: code
                    })
                });

                if (response.success) {
                    alert(`激活成功！增加了${response.data.days_added}天使用时间`);
                    // 激活成功后跳转到主界面
                    window.location.href = 'popup.html';
                } else {
                    alert(response.message || '激活失败');
                }
            } catch (error) {
                console.error('激活失败:', error);
                alert('激活失败，请检查网络连接');
            } finally {
                expiredActivateBtn.disabled = false;
                expiredActivateBtn.textContent = '激活';
            }
        });

        // 回车键激活
        expiredActivationCode.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                expiredActivateBtn.click();
            }
        });
    }

    // 更新购买链接
    function updatePurchaseLink(url) {
        if (url && url !== '#') {
            purchaseLink.href = url;
            purchaseLink.style.display = 'inline';
        }
    }

    // 显示消息
    function showMessage(text, type = 'info') {
        message.textContent = text;
        message.className = `message ${type}`;
        message.style.display = 'block';

        // 3秒后自动隐藏
        setTimeout(hideMessage, 3000);
    }

    // 隐藏消息
    function hideMessage() {
        message.style.display = 'none';
    }

    // 设置加载状态
    function setLoading(isLoading) {
        loading.style.display = isLoading ? 'block' : 'none';

        // 禁用/启用按钮
        const buttons = [loginBtn, registerBtn, activateBtn];
        buttons.forEach(btn => {
            btn.disabled = isLoading;
        });
    }

    // 存储用户数据
    async function storeUserData(userData) {
        return new Promise((resolve) => {
            chrome.storage.local.set({ 'user_data': userData }, resolve);
        });
    }

    // 获取存储的用户数据
    async function getStoredUserData() {
        return new Promise((resolve) => {
            chrome.storage.local.get(['user_data'], (result) => {
                resolve(result.user_data || null);
            });
        });
    }

    // 清除存储的用户数据
    async function clearStoredUserData() {
        return new Promise((resolve) => {
            chrome.storage.local.remove(['user_data'], resolve);
        });
    }
});
