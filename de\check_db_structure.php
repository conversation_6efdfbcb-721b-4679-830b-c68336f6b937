<?php
// 数据库结构检查工具
header('Content-Type: text/html; charset=utf-8');
require_once 'config.php';

$pdo = getDBConnection();
if (!$pdo) {
    die('数据库连接失败');
}

// 获取所有表
$tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库结构检查</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        h1, h2 {
            color: #333;
        }
        .table-info {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #e9ecef;
            font-weight: bold;
        }
        .field-type {
            color: #6c757d;
            font-family: monospace;
        }
        .nullable {
            color: #28a745;
        }
        .not-nullable {
            color: #dc3545;
        }
        .primary-key {
            background-color: #fff3cd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ 数据库结构检查</h1>
        
        <p><strong>数据库：</strong> timezone</p>
        <p><strong>表数量：</strong> <?php echo count($tables); ?></p>
        
        <?php foreach ($tables as $table): ?>
            <div class="table-info">
                <h2>📋 表：<?php echo $table; ?></h2>
                
                <?php
                // 获取表结构
                $columns = $pdo->query("DESCRIBE `$table`")->fetchAll(PDO::FETCH_ASSOC);
                
                // 获取表数据量
                $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
                ?>
                
                <p><strong>记录数：</strong> <?php echo $count; ?></p>
                
                <table>
                    <thead>
                        <tr>
                            <th>字段名</th>
                            <th>数据类型</th>
                            <th>允许NULL</th>
                            <th>键</th>
                            <th>默认值</th>
                            <th>额外信息</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($columns as $column): ?>
                            <tr <?php echo $column['Key'] === 'PRI' ? 'class="primary-key"' : ''; ?>>
                                <td><strong><?php echo $column['Field']; ?></strong></td>
                                <td class="field-type"><?php echo $column['Type']; ?></td>
                                <td class="<?php echo $column['Null'] === 'YES' ? 'nullable' : 'not-nullable'; ?>">
                                    <?php echo $column['Null'] === 'YES' ? 'YES' : 'NO'; ?>
                                </td>
                                <td><?php echo $column['Key']; ?></td>
                                <td><?php echo $column['Default'] ?? 'NULL'; ?></td>
                                <td><?php echo $column['Extra']; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <?php if ($table === 'users' && $count > 0): ?>
                    <h3>👥 用户数据示例</h3>
                    <?php
                    $sampleUsers = $pdo->query("SELECT * FROM users LIMIT 3")->fetchAll(PDO::FETCH_ASSOC);
                    ?>
                    <table>
                        <thead>
                            <tr>
                                <?php foreach (array_keys($sampleUsers[0]) as $field): ?>
                                    <th><?php echo $field; ?></th>
                                <?php endforeach; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($sampleUsers as $user): ?>
                                <tr>
                                    <?php foreach ($user as $value): ?>
                                        <td><?php echo htmlspecialchars($value ?? 'NULL'); ?></td>
                                    <?php endforeach; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
        
        <div style="margin-top: 30px; padding: 20px; background: #e3f2fd; border-radius: 8px;">
            <h3>💡 说明</h3>
            <ul>
                <li><strong>黄色背景</strong>：主键字段</li>
                <li><strong>绿色</strong>：允许NULL值</li>
                <li><strong>红色</strong>：不允许NULL值</li>
                <li>此工具显示所有表的结构和示例数据</li>
            </ul>
        </div>
    </div>
</body>
</html>
