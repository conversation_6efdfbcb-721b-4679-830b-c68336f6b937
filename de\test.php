<?php
// API测试页面
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试 - Timezone Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #666;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 Timezone Extension API 测试</h1>
        
        <div class="test-section">
            <h3>1. 数据库连接测试</h3>
            <?php
            try {
                require_once 'config.php';
                $pdo = getDBConnection();
                if ($pdo) {
                    echo '<div class="result success">✅ 数据库连接成功</div>';
                    
                    // 测试查询
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
                    $result = $stmt->fetch();
                    echo '<div class="result success">📊 用户表记录数: ' . $result['count'] . '</div>';
                    
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM activation_codes");
                    $result = $stmt->fetch();
                    echo '<div class="result success">🎫 激活码表记录数: ' . $result['count'] . '</div>';
                    
                } else {
                    echo '<div class="result error">❌ 数据库连接失败</div>';
                }
            } catch (Exception $e) {
                echo '<div class="result error">❌ 数据库连接错误: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>

        <div class="test-section">
            <h3>2. 系统配置测试</h3>
            <?php
            try {
                if (isset($pdo)) {
                    $defaultTrialDays = getSystemConfig($pdo, 'default_trial_days');
                    $purchaseLink = getSystemConfig($pdo, 'purchase_link');
                    $maxDevices = getSystemConfig($pdo, 'max_devices_per_user');
                    
                    echo '<div class="result success">⏰ 默认试用天数: ' . ($defaultTrialDays ?: '未设置') . '</div>';
                    echo '<div class="result success">🛒 购买链接: ' . ($purchaseLink ?: '未设置') . '</div>';
                    echo '<div class="result success">📱 每用户最大设备数: ' . ($maxDevices ?: '未设置') . '</div>';
                }
            } catch (Exception $e) {
                echo '<div class="result error">❌ 系统配置读取错误: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>

        <div class="test-section">
            <h3>3. API端点测试</h3>
            <?php
            $apiFiles = ['register.php', 'login.php', 'activate.php', 'user_status.php'];
            foreach ($apiFiles as $file) {
                if (file_exists($file)) {
                    echo '<div class="result success">✅ ' . $file . ' 文件存在</div>';
                } else {
                    echo '<div class="result error">❌ ' . $file . ' 文件不存在</div>';
                }
            }
            ?>
        </div>

        <div class="test-section">
            <h3>4. 设备指纹生成测试</h3>
            <?php
            try {
                $testFingerprint = generateDeviceFingerprint();
                echo '<div class="result success">🔍 测试设备指纹: ' . $testFingerprint . '</div>';
            } catch (Exception $e) {
                echo '<div class="result error">❌ 设备指纹生成错误: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>

        <div class="test-section">
            <h3>5. 密码哈希测试</h3>
            <?php
            try {
                $testPassword = 'test123456';
                $hash = hashPassword($testPassword);
                $verify = verifyPassword($testPassword, $hash);
                
                echo '<div class="result success">🔐 密码哈希: ' . substr($hash, 0, 30) . '...</div>';
                echo '<div class="result ' . ($verify ? 'success' : 'error') . '">' . 
                     ($verify ? '✅' : '❌') . ' 密码验证: ' . ($verify ? '通过' : '失败') . '</div>';
            } catch (Exception $e) {
                echo '<div class="result error">❌ 密码哈希测试错误: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>

        <div class="test-section">
            <h3>6. 客户端IP获取测试</h3>
            <?php
            try {
                $clientIP = getClientIP();
                echo '<div class="result success">🌐 客户端IP: ' . $clientIP . '</div>';
            } catch (Exception $e) {
                echo '<div class="result error">❌ IP获取错误: ' . $e->getMessage() . '</div>';
            }
            ?>
        </div>

        <div class="test-section">
            <h3>7. 服务器信息</h3>
            <div class="result success">
                🖥️ PHP版本: <?php echo PHP_VERSION; ?><br>
                📅 服务器时间: <?php echo date('Y-m-d H:i:s'); ?><br>
                🌍 时区: <?php echo date_default_timezone_get(); ?><br>
                📂 当前目录: <?php echo __DIR__; ?>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px; color: #666;">
            <p>🔧 API测试完成 - <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
    </div>
</body>
</html>
