<?php
// 客服联系方式配置页面
header('Content-Type: text/html; charset=utf-8');
require_once 'config.php';

$pdo = getDBConnection();
if (!$pdo) {
    die('数据库连接失败');
}

// 处理表单提交
if ($_POST) {
    $serviceQQ = trim($_POST['service_qq'] ?? '');
    $serviceWechat = trim($_POST['service_wechat'] ?? '');
    $servicePhone = trim($_POST['service_phone'] ?? '');
    $serviceEmail = trim($_POST['service_email'] ?? '');
    $serviceHours = trim($_POST['service_hours'] ?? '');
    
    try {
        // 更新或插入客服配置
        $configs = [
            'service_qq' => $serviceQQ,
            'service_wechat' => $serviceWechat,
            'service_phone' => $servicePhone,
            'service_email' => $serviceEmail,
            'service_hours' => $serviceHours
        ];
        
        foreach ($configs as $key => $value) {
            if ($value) {
                $stmt = $pdo->prepare("
                    INSERT INTO system_config (config_key, config_value, description) 
                    VALUES (?, ?, ?) 
                    ON DUPLICATE KEY UPDATE 
                    config_value = VALUES(config_value), 
                    updated_at = NOW()
                ");
                
                $descriptions = [
                    'service_qq' => '客服QQ号码',
                    'service_wechat' => '客服微信号',
                    'service_phone' => '客服电话',
                    'service_email' => '客服邮箱',
                    'service_hours' => '客服工作时间'
                ];
                
                $stmt->execute([$key, $value, $descriptions[$key]]);
            }
        }
        
        $message = "客服信息配置成功！";
        $messageType = "success";
    } catch (Exception $e) {
        $message = "配置失败：" . $e->getMessage();
        $messageType = "error";
    }
}

// 获取当前配置
$currentConfigs = [
    'service_qq' => getSystemConfig($pdo, 'service_qq') ?: '',
    'service_wechat' => getSystemConfig($pdo, 'service_wechat') ?: '',
    'service_phone' => getSystemConfig($pdo, 'service_phone') ?: '',
    'service_email' => getSystemConfig($pdo, 'service_email') ?: '',
    'service_hours' => getSystemConfig($pdo, 'service_hours') ?: '9:00-18:00'
];

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>客服联系方式配置</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="email"], input[type="tel"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }
        input:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .current-config {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .current-config h3 {
            margin-top: 0;
            color: #495057;
        }
        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .config-item:last-child {
            border-bottom: none;
        }
        .config-label {
            font-weight: bold;
            color: #495057;
        }
        .config-value {
            color: #6c757d;
            font-family: monospace;
        }
        .preview-section {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        .preview-message {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
            font-family: monospace;
            font-size: 14px;
            line-height: 1.6;
        }
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💬 客服联系方式配置</h1>
        
        <?php if (isset($message)): ?>
            <div class="message <?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <div class="current-config">
            <h3>📋 当前配置</h3>
            <div class="config-item">
                <span class="config-label">客服QQ：</span>
                <span class="config-value"><?php echo $currentConfigs['service_qq'] ?: '未配置'; ?></span>
            </div>
            <div class="config-item">
                <span class="config-label">客服微信：</span>
                <span class="config-value"><?php echo $currentConfigs['service_wechat'] ?: '未配置'; ?></span>
            </div>
            <div class="config-item">
                <span class="config-label">客服电话：</span>
                <span class="config-value"><?php echo $currentConfigs['service_phone'] ?: '未配置'; ?></span>
            </div>
            <div class="config-item">
                <span class="config-label">客服邮箱：</span>
                <span class="config-value"><?php echo $currentConfigs['service_email'] ?: '未配置'; ?></span>
            </div>
            <div class="config-item">
                <span class="config-label">工作时间：</span>
                <span class="config-value"><?php echo $currentConfigs['service_hours'] ?: '未配置'; ?></span>
            </div>
        </div>
        
        <form method="POST">
            <div class="form-grid">
                <div class="form-group">
                    <label for="service_qq">客服QQ号码：</label>
                    <input 
                        type="text" 
                        id="service_qq" 
                        name="service_qq" 
                        value="<?php echo htmlspecialchars($currentConfigs['service_qq']); ?>"
                        placeholder="例如：123456789"
                    >
                </div>
                
                <div class="form-group">
                    <label for="service_wechat">客服微信号：</label>
                    <input 
                        type="text" 
                        id="service_wechat" 
                        name="service_wechat" 
                        value="<?php echo htmlspecialchars($currentConfigs['service_wechat']); ?>"
                        placeholder="例如：service123"
                    >
                </div>
                
                <div class="form-group">
                    <label for="service_phone">客服电话：</label>
                    <input 
                        type="tel" 
                        id="service_phone" 
                        name="service_phone" 
                        value="<?php echo htmlspecialchars($currentConfigs['service_phone']); ?>"
                        placeholder="例如：************"
                    >
                </div>
                
                <div class="form-group">
                    <label for="service_email">客服邮箱：</label>
                    <input 
                        type="email" 
                        id="service_email" 
                        name="service_email" 
                        value="<?php echo htmlspecialchars($currentConfigs['service_email']); ?>"
                        placeholder="例如：<EMAIL>"
                    >
                </div>
                
                <div class="form-group full-width">
                    <label for="service_hours">工作时间：</label>
                    <input 
                        type="text" 
                        id="service_hours" 
                        name="service_hours" 
                        value="<?php echo htmlspecialchars($currentConfigs['service_hours']); ?>"
                        placeholder="例如：9:00-18:00 (周一至周五)"
                    >
                </div>
            </div>
            
            <button type="submit" class="btn">💾 保存配置</button>
        </form>
        
        <div class="preview-section">
            <h3>👀 用户看到的提示预览</h3>
            <div class="preview-message" id="preview">
                <!-- 预览内容将通过JavaScript生成 -->
            </div>
        </div>
    </div>

    <script>
        function updatePreview() {
            const qq = document.getElementById('service_qq').value;
            const wechat = document.getElementById('service_wechat').value;
            const phone = document.getElementById('service_phone').value;
            const email = document.getElementById('service_email').value;
            const hours = document.getElementById('service_hours').value;
            
            let preview = '需要帮助？请联系我们的客服：\n\n';
            
            if (qq) preview += `📱 QQ：${qq}\n`;
            if (wechat) preview += `💬 微信：${wechat}\n`;
            if (phone) preview += `📞 电话：${phone}\n`;
            if (email) preview += `📧 邮箱：${email}\n`;
            if (hours) preview += `⏰ 工作时间：${hours}\n`;
            
            if (!qq && !wechat && !phone && !email) {
                preview = '请联系客服获取帮助';
            }
            
            document.getElementById('preview').textContent = preview;
        }
        
        // 监听输入变化
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('input', updatePreview);
        });
        
        // 初始化预览
        updatePreview();
    </script>
</body>
</html>
