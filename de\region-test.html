<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地区选择测试 - Global Timezone Spoofer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-card h3 {
            margin-top: 0;
            color: #fff;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .result {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            margin: 8px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            border-left: 3px solid #4CAF50;
        }
        
        .error {
            border-left-color: #f44336;
            background: rgba(244, 67, 54, 0.1);
        }
        
        .refresh-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #2196F3;
        }
        
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 8px 0;
            line-height: 1.5;
        }
        
        .current-settings {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .current-settings h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .auto-refresh {
            text-align: center;
            margin: 20px 0;
        }
        
        .auto-refresh label {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🌍 Global Timezone & Location Spoofer</h1>
        <h2>地区选择功能测试页面</h2>
        <p>v2.9.0 - User Region Selection with Persistent Settings</p>
    </div>
    
    <div class="instructions">
        <h3>📋 使用说明</h3>
        <ol>
            <li>点击浏览器工具栏中的扩展图标打开设置面板</li>
            <li>选择你想要模拟的地区（美国🇺🇸、台湾🇹🇼、日本🇯🇵、新加坡🇸🇬）</li>
            <li>点击"应用设置"按钮</li>
            <li>页面会自动刷新，显示新的地区设置</li>
            <li>所有新打开的页面都会使用相同的地区设置</li>
        </ol>
    </div>
    
    <div class="current-settings" id="current-settings">
        <h3>🎯 当前设置</h3>
        <div id="settings-info">正在加载...</div>
    </div>
    
    <div class="auto-refresh">
        <label>
            <input type="checkbox" id="auto-refresh" checked>
            <span>自动刷新测试结果 (每5秒)</span>
        </label>
    </div>
    
    <div class="test-grid">
        <div class="test-card">
            <h3>📅 日期时间测试</h3>
            <button class="refresh-btn" onclick="testDateTime()">刷新测试</button>
            <div id="datetime-results"></div>
        </div>
        
        <div class="test-card">
            <h3>🌐 时区测试</h3>
            <button class="refresh-btn" onclick="testTimezone()">刷新测试</button>
            <div id="timezone-results"></div>
        </div>
        
        <div class="test-card">
            <h3>📍 地理位置测试</h3>
            <button class="refresh-btn" onclick="testGeolocation()">刷新测试</button>
            <div id="geolocation-results"></div>
        </div>
        
        <div class="test-card">
            <h3>🗣️ 语言测试</h3>
            <button class="refresh-btn" onclick="testLanguage()">刷新测试</button>
            <div id="language-results"></div>
        </div>
    </div>
    
    <script>
        let autoRefreshInterval;
        
        function addResult(containerId, content, isError = false) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = isError ? 'result error' : 'result';
            div.innerHTML = content;
            container.appendChild(div);
        }
        
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }
        
        function loadCurrentSettings() {
            const storageKey = 'globalTimezoneSpoofer_selectedCity';
            const savedSettings = localStorage.getItem(storageKey);
            const settingsInfo = document.getElementById('settings-info');
            
            if (savedSettings) {
                try {
                    const cityData = JSON.parse(savedSettings);
                    const flagMap = {
                        'US': '🇺🇸',
                        'TW': '🇹🇼', 
                        'JP': '🇯🇵',
                        'SG': '🇸🇬'
                    };
                    
                    settingsInfo.innerHTML = `
                        <div><strong>地区:</strong> ${flagMap[cityData.country_code] || '🌍'} ${cityData.country}</div>
                        <div><strong>城市:</strong> ${cityData.city}, ${cityData.state}</div>
                        <div><strong>时区:</strong> ${cityData.timezone}</div>
                        <div><strong>语言:</strong> ${cityData.locale}</div>
                        <div><strong>坐标:</strong> ${cityData.lat.toFixed(4)}, ${cityData.lng.toFixed(4)}</div>
                    `;
                } catch (e) {
                    settingsInfo.innerHTML = '<div style="color: #f44336;">设置解析失败</div>';
                }
            } else {
                settingsInfo.innerHTML = '<div style="color: #ff9800;">未找到保存的设置，使用随机地区</div>';
            }
        }
        
        function testDateTime() {
            clearResults('datetime-results');
            try {
                const now = new Date();
                addResult('datetime-results', `当前时间: ${now.toString()}`);
                addResult('datetime-results', `本地时间: ${now.toLocaleString()}`);
                addResult('datetime-results', `UTC时间: ${now.toUTCString()}`);
                addResult('datetime-results', `时区偏移: ${now.getTimezoneOffset()} 分钟`);
                addResult('datetime-results', `Date.now(): ${Date.now()}`);
            } catch (e) {
                addResult('datetime-results', `错误: ${e.message}`, true);
            }
        }
        
        function testTimezone() {
            clearResults('timezone-results');
            try {
                const formatter = new Intl.DateTimeFormat();
                const options = formatter.resolvedOptions();
                addResult('timezone-results', `检测到的时区: ${options.timeZone}`);
                addResult('timezone-results', `检测到的语言: ${options.locale}`);
                
                const now = new Date();
                const timeInTimezone = now.toLocaleString('en-US', {timeZone: options.timeZone});
                addResult('timezone-results', `时区时间: ${timeInTimezone}`);
            } catch (e) {
                addResult('timezone-results', `错误: ${e.message}`, true);
            }
        }
        
        function testGeolocation() {
            clearResults('geolocation-results');
            
            if (!navigator.geolocation) {
                addResult('geolocation-results', '地理位置API不支持', true);
                return;
            }
            
            addResult('geolocation-results', '正在获取位置...');
            
            navigator.geolocation.getCurrentPosition(
                (position) => {
                    clearResults('geolocation-results');
                    addResult('geolocation-results', `纬度: ${position.coords.latitude}`);
                    addResult('geolocation-results', `经度: ${position.coords.longitude}`);
                    addResult('geolocation-results', `精度: ${position.coords.accuracy} 米`);
                    addResult('geolocation-results', `时间戳: ${new Date(position.timestamp).toLocaleString()}`);
                },
                (error) => {
                    clearResults('geolocation-results');
                    addResult('geolocation-results', `地理位置错误: ${error.message}`, true);
                },
                { timeout: 10000, maximumAge: 0 }
            );
        }
        
        function testLanguage() {
            clearResults('language-results');
            try {
                addResult('language-results', `Navigator语言: ${navigator.language}`);
                addResult('language-results', `支持的语言: ${navigator.languages.join(', ')}`);
                
                const number = 1234567.89;
                addResult('language-results', `数字格式: ${number.toLocaleString()}`);
                
                const date = new Date();
                addResult('language-results', `日期格式: ${date.toLocaleDateString()}`);
                addResult('language-results', `时间格式: ${date.toLocaleTimeString()}`);
            } catch (e) {
                addResult('language-results', `错误: ${e.message}`, true);
            }
        }
        
        function runAllTests() {
            loadCurrentSettings();
            testDateTime();
            testTimezone();
            testGeolocation();
            testLanguage();
        }
        
        // 自动刷新功能
        document.getElementById('auto-refresh').addEventListener('change', function() {
            if (this.checked) {
                autoRefreshInterval = setInterval(runAllTests, 5000);
            } else {
                clearInterval(autoRefreshInterval);
            }
        });
        
        // 页面加载时运行测试
        window.addEventListener('load', () => {
            runAllTests();
            // 启动自动刷新
            autoRefreshInterval = setInterval(runAllTests, 5000);
        });
        
        // 监听localStorage变化
        window.addEventListener('storage', (e) => {
            if (e.key === 'globalTimezoneSpoofer_selectedCity') {
                console.log('🔄 检测到设置变化，刷新测试');
                setTimeout(runAllTests, 1000);
            }
        });
    </script>
</body>
</html>
