{"version": 3, "file": "metadata.test.js", "names": ["metadata", "type", "metadataV1", "metadataV2", "metadataV3", "metadataV4", "<PERSON><PERSON><PERSON>", "validateMetadata", "getExtPrefix", "isSupportedCountry", "describe", "it", "FR", "country", "should", "equal", "thrower", "getNumberingPlanMetadata", "nationalPrefixForParsing", "chooseCountryByCountryCallingCode", "meta", "numberingPlan", "formats", "nationalPrefixIsMandatoryWhenFormattingInNationalFormat", "a", "b", "countries", "country_calling_codes", "selectNumberingPlan", "nonGeographic", "possibleLengths", "deep", "length", "nationalPrefix", "pattern", "leadingDigits", "expect", "nationalPrefixTransformRule", "to", "be", "nationalPrefixFormattingRule", "nationalPrefixIsOptionalWhenFormattingInNationalFormat", "ext", "metaNew", "defaultIDDPrefix", "something"], "sources": ["../source/metadata.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' assert { type: 'json' }\r\nimport metadataV1 from '../test/metadata/1.0.0/metadata.min.json' assert { type: 'json' }\r\nimport metadataV2 from '../test/metadata/1.1.11/metadata.min.json' assert { type: 'json' }\r\nimport metadataV3 from '../test/metadata/1.7.34/metadata.min.json' assert { type: 'json' }\r\nimport metadataV4 from '../test/metadata/1.7.37/metadata.min.json' assert { type: 'json' }\r\n\r\nimport Metadata, { validateMetadata, getExtPrefix, isSupportedCountry } from './metadata.js'\r\n\r\ndescribe('metadata', () => {\r\n\tit('should return undefined for non-defined types', () => {\r\n\t\tconst FR = new Metadata(metadata).country('FR')\r\n\t\ttype(FR.type('FIXED_LINE')).should.equal('undefined')\r\n\t})\r\n\r\n\tit('should validate country', () => {\r\n\t\tconst thrower = () => new Metadata(metadata).country('RUS')\r\n\t\tthrower.should.throw('Unknown country')\r\n\t})\r\n\r\n\tit('should tell if a country is supported', () => {\r\n\t\tisSupportedCountry('RU', metadata).should.equal(true)\r\n\t\tisSupportedCountry('XX', metadata).should.equal(false)\r\n\t})\r\n\r\n\tit('should return ext prefix for a country', () => {\r\n\t\tgetExtPrefix('US', metadata).should.equal(' ext. ')\r\n\t\tgetExtPrefix('CA', metadata).should.equal(' ext. ')\r\n\t\tgetExtPrefix('GB', metadata).should.equal(' x')\r\n\t\t// expect(getExtPrefix('XX', metadata)).to.equal(undefined)\r\n\t\tgetExtPrefix('XX', metadata).should.equal(' ext. ')\r\n\t})\r\n\r\n\tit('should cover non-occuring edge cases', () => {\r\n\t\tnew Metadata(metadata).getNumberingPlanMetadata('999')\r\n\t})\r\n\r\n\tit('should support deprecated methods', () => {\r\n\t\tnew Metadata(metadata).country('US').nationalPrefixForParsing().should.equal('1')\r\n\t\tnew Metadata(metadata).chooseCountryByCountryCallingCode('1').nationalPrefixForParsing().should.equal('1')\r\n\t})\r\n\r\n\tit('should tell if a national prefix is mandatory when formatting a national number', () => {\r\n\t\tconst meta = new Metadata(metadata)\r\n\t\t// No \"national_prefix_formatting_rule\".\r\n\t\t// \"national_prefix_is_optional_when_formatting\": true\r\n\t\tmeta.country('US')\r\n\t\tmeta.numberingPlan.formats()[0].nationalPrefixIsMandatoryWhenFormattingInNationalFormat().should.equal(false)\r\n\t\t// \"national_prefix_formatting_rule\": \"8 ($1)\"\r\n\t\t// \"national_prefix_is_optional_when_formatting\": true\r\n\t\tmeta.country('RU')\r\n\t\tmeta.numberingPlan.formats()[0].nationalPrefixIsMandatoryWhenFormattingInNationalFormat().should.equal(false)\r\n\t\t// \"national_prefix\": \"0\"\r\n\t\t// \"national_prefix_formatting_rule\": \"0 $1\"\r\n\t\tmeta.country('FR')\r\n\t\tmeta.numberingPlan.formats()[0].nationalPrefixIsMandatoryWhenFormattingInNationalFormat().should.equal(true)\r\n\t})\r\n\r\n\tit('should validate metadata', () => {\r\n\t\tlet thrower = () => validateMetadata()\r\n\t\tthrower.should.throw('`metadata` argument not passed')\r\n\r\n\t\tthrower = () => validateMetadata(123)\r\n\t\tthrower.should.throw('Got a number: 123.')\r\n\r\n\t\tthrower = () => validateMetadata('abc')\r\n\t\tthrower.should.throw('Got a string: abc.')\r\n\r\n\t\tthrower = () => validateMetadata({ a: true, b: 2 })\r\n\t\tthrower.should.throw('Got an object of shape: { a, b }.')\r\n\r\n\t\tthrower = () => validateMetadata({ a: true, countries: 2 })\r\n\t\tthrower.should.throw('Got an object of shape: { a, countries }.')\r\n\r\n\t\tthrower = () => validateMetadata({ country_calling_codes: true, countries: 2 })\r\n\t\tthrower.should.throw('Got an object of shape')\r\n\r\n\t\tthrower = () => validateMetadata({ country_calling_codes: {}, countries: 2 })\r\n\t\tthrower.should.throw('Got an object of shape')\r\n\r\n\t\tvalidateMetadata({ country_calling_codes: {}, countries: {}, b: 3 })\r\n\t})\r\n\r\n\tit('should work around `nonGeographical` typo in metadata generated from `1.7.35` to `1.7.37`', function() {\r\n\t\tconst meta = new Metadata(metadataV4)\r\n\t\tmeta.selectNumberingPlan('888')\r\n\t\ttype(meta.nonGeographic()).should.equal('object')\r\n\t})\r\n\r\n\tit('should work around `nonGeographic` metadata not existing before `1.7.35`', function() {\r\n\t\tconst meta = new Metadata(metadataV3)\r\n\t\ttype(meta.getNumberingPlanMetadata('800')).should.equal('object')\r\n\t\ttype(meta.getNumberingPlanMetadata('000')).should.equal('undefined')\r\n\t})\r\n\r\n\tit('should work with metadata from version `1.1.11`', function() {\r\n\t\tconst meta = new Metadata(metadataV2)\r\n\r\n\t\tmeta.selectNumberingPlan('US')\r\n\t\tmeta.numberingPlan.possibleLengths().should.deep.equal([10])\r\n\t\tmeta.numberingPlan.formats().length.should.equal(1)\r\n\t\tmeta.numberingPlan.nationalPrefix().should.equal('1')\r\n\t\tmeta.numberingPlan.nationalPrefixForParsing().should.equal('1')\r\n\t\tmeta.numberingPlan.type('MOBILE').pattern().should.equal('')\r\n\r\n\t\tmeta.selectNumberingPlan('AG')\r\n\t\tmeta.numberingPlan.leadingDigits().should.equal('268')\r\n\t\t// Should've been \"268$1\" but apparently there was a bug in metadata generator\r\n\t\t// and no national prefix transform rules were written.\r\n\t\texpect(meta.numberingPlan.nationalPrefixTransformRule()).to.be.null\r\n\r\n\t\tmeta.selectNumberingPlan('AF')\r\n\t\tmeta.numberingPlan.formats()[0].nationalPrefixFormattingRule().should.equal('0$1')\r\n\r\n\t\tmeta.selectNumberingPlan('RU')\r\n\t\tmeta.numberingPlan.formats()[0].nationalPrefixIsOptionalWhenFormattingInNationalFormat().should.equal(true)\r\n\t})\r\n\r\n\tit('should work with metadata from version `1.0.0`', function() {\r\n\t\tconst meta = new Metadata(metadataV1)\r\n\r\n\t\tmeta.selectNumberingPlan('US')\r\n\t\tmeta.numberingPlan.formats().length.should.equal(1)\r\n\t\tmeta.numberingPlan.nationalPrefix().should.equal('1')\r\n\t\tmeta.numberingPlan.nationalPrefixForParsing().should.equal('1')\r\n\t\ttype(meta.numberingPlan.type('MOBILE')).should.equal('undefined')\r\n\r\n\t\tmeta.selectNumberingPlan('AG')\r\n\t\tmeta.numberingPlan.leadingDigits().should.equal('268')\r\n\t\t// Should've been \"268$1\" but apparently there was a bug in metadata generator\r\n\t\t// and no national prefix transform rules were written.\r\n\t\texpect(meta.numberingPlan.nationalPrefixTransformRule()).to.be.null\r\n\r\n\t\tmeta.selectNumberingPlan('AF')\r\n\t\tmeta.numberingPlan.formats()[0].nationalPrefixFormattingRule().should.equal('0$1')\r\n\r\n\t\tmeta.selectNumberingPlan('RU')\r\n\t\tmeta.numberingPlan.formats()[0].nationalPrefixIsOptionalWhenFormattingInNationalFormat().should.equal(true)\r\n\t})\r\n\r\n\tit('should work around \"ext\" data not present in metadata from version `1.0.0`', function() {\r\n\t\tconst meta = new Metadata(metadataV1)\r\n\t\tmeta.selectNumberingPlan('GB')\r\n\t\tmeta.ext().should.equal(' ext. ')\r\n\r\n\t\tconst metaNew = new Metadata(metadata)\r\n\t\tmetaNew.selectNumberingPlan('GB')\r\n\t\tmetaNew.ext().should.equal(' x')\r\n\t})\r\n\r\n\tit('should work around \"default IDD prefix\" data not present in metadata from version `1.0.0`', function() {\r\n\t\tconst meta = new Metadata(metadataV1)\r\n\t\tmeta.selectNumberingPlan('AU')\r\n\t\ttype(meta.defaultIDDPrefix()).should.equal('undefined')\r\n\r\n\t\tconst metaNew = new Metadata(metadata)\r\n\t\tmetaNew.selectNumberingPlan('AU')\r\n\t\tmetaNew.defaultIDDPrefix().should.equal('0011')\r\n\t})\r\n})\r\n\r\nfunction type(something) {\r\n\treturn typeof something\r\n}"], "mappings": ";;AAAA,OAAOA,QAAP,MAAqB,sBAArB,UAAqDC,IAAI,EAAE,MAA3D;AACA,OAAOC,UAAP,MAAuB,0CAAvB,UAA2ED,IAAI,EAAE,MAAjF;AACA,OAAOE,UAAP,MAAuB,2CAAvB,UAA4EF,IAAI,EAAE,MAAlF;AACA,OAAOG,UAAP,MAAuB,2CAAvB,UAA4EH,IAAI,EAAE,MAAlF;AACA,OAAOI,UAAP,MAAuB,2CAAvB,UAA4EJ,IAAI,EAAE,MAAlF;AAEA,OAAOK,QAAP,IAAmBC,gBAAnB,EAAqCC,YAArC,EAAmDC,kBAAnD,QAA6E,eAA7E;AAEAC,QAAQ,CAAC,UAAD,EAAa,YAAM;EAC1BC,EAAE,CAAC,+CAAD,EAAkD,YAAM;IACzD,IAAMC,EAAE,GAAG,IAAIN,QAAJ,CAAaN,QAAb,EAAuBa,OAAvB,CAA+B,IAA/B,CAAX;IACAZ,IAAI,CAACW,EAAE,CAACX,IAAH,CAAQ,YAAR,CAAD,CAAJ,CAA4Ba,MAA5B,CAAmCC,KAAnC,CAAyC,WAAzC;EACA,CAHC,CAAF;EAKAJ,EAAE,CAAC,yBAAD,EAA4B,YAAM;IACnC,IAAMK,OAAO,GAAG,SAAVA,OAAU;MAAA,OAAM,IAAIV,QAAJ,CAAaN,QAAb,EAAuBa,OAAvB,CAA+B,KAA/B,CAAN;IAAA,CAAhB;;IACAG,OAAO,CAACF,MAAR,UAAqB,iBAArB;EACA,CAHC,CAAF;EAKAH,EAAE,CAAC,uCAAD,EAA0C,YAAM;IACjDF,kBAAkB,CAAC,IAAD,EAAOT,QAAP,CAAlB,CAAmCc,MAAnC,CAA0CC,KAA1C,CAAgD,IAAhD;IACAN,kBAAkB,CAAC,IAAD,EAAOT,QAAP,CAAlB,CAAmCc,MAAnC,CAA0CC,KAA1C,CAAgD,KAAhD;EACA,CAHC,CAAF;EAKAJ,EAAE,CAAC,wCAAD,EAA2C,YAAM;IAClDH,YAAY,CAAC,IAAD,EAAOR,QAAP,CAAZ,CAA6Bc,MAA7B,CAAoCC,KAApC,CAA0C,QAA1C;IACAP,YAAY,CAAC,IAAD,EAAOR,QAAP,CAAZ,CAA6Bc,MAA7B,CAAoCC,KAApC,CAA0C,QAA1C;IACAP,YAAY,CAAC,IAAD,EAAOR,QAAP,CAAZ,CAA6Bc,MAA7B,CAAoCC,KAApC,CAA0C,IAA1C,EAHkD,CAIlD;;IACAP,YAAY,CAAC,IAAD,EAAOR,QAAP,CAAZ,CAA6Bc,MAA7B,CAAoCC,KAApC,CAA0C,QAA1C;EACA,CANC,CAAF;EAQAJ,EAAE,CAAC,sCAAD,EAAyC,YAAM;IAChD,IAAIL,QAAJ,CAAaN,QAAb,EAAuBiB,wBAAvB,CAAgD,KAAhD;EACA,CAFC,CAAF;EAIAN,EAAE,CAAC,mCAAD,EAAsC,YAAM;IAC7C,IAAIL,QAAJ,CAAaN,QAAb,EAAuBa,OAAvB,CAA+B,IAA/B,EAAqCK,wBAArC,GAAgEJ,MAAhE,CAAuEC,KAAvE,CAA6E,GAA7E;IACA,IAAIT,QAAJ,CAAaN,QAAb,EAAuBmB,iCAAvB,CAAyD,GAAzD,EAA8DD,wBAA9D,GAAyFJ,MAAzF,CAAgGC,KAAhG,CAAsG,GAAtG;EACA,CAHC,CAAF;EAKAJ,EAAE,CAAC,iFAAD,EAAoF,YAAM;IAC3F,IAAMS,IAAI,GAAG,IAAId,QAAJ,CAAaN,QAAb,CAAb,CAD2F,CAE3F;IACA;;IACAoB,IAAI,CAACP,OAAL,CAAa,IAAb;IACAO,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6B,CAA7B,EAAgCC,uDAAhC,GAA0FT,MAA1F,CAAiGC,KAAjG,CAAuG,KAAvG,EAL2F,CAM3F;IACA;;IACAK,IAAI,CAACP,OAAL,CAAa,IAAb;IACAO,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6B,CAA7B,EAAgCC,uDAAhC,GAA0FT,MAA1F,CAAiGC,KAAjG,CAAuG,KAAvG,EAT2F,CAU3F;IACA;;IACAK,IAAI,CAACP,OAAL,CAAa,IAAb;IACAO,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6B,CAA7B,EAAgCC,uDAAhC,GAA0FT,MAA1F,CAAiGC,KAAjG,CAAuG,IAAvG;EACA,CAdC,CAAF;EAgBAJ,EAAE,CAAC,0BAAD,EAA6B,YAAM;IACpC,IAAIK,OAAO,GAAG;MAAA,OAAMT,gBAAgB,EAAtB;IAAA,CAAd;;IACAS,OAAO,CAACF,MAAR,UAAqB,gCAArB;;IAEAE,OAAO,GAAG;MAAA,OAAMT,gBAAgB,CAAC,GAAD,CAAtB;IAAA,CAAV;;IACAS,OAAO,CAACF,MAAR,UAAqB,oBAArB;;IAEAE,OAAO,GAAG;MAAA,OAAMT,gBAAgB,CAAC,KAAD,CAAtB;IAAA,CAAV;;IACAS,OAAO,CAACF,MAAR,UAAqB,oBAArB;;IAEAE,OAAO,GAAG;MAAA,OAAMT,gBAAgB,CAAC;QAAEiB,CAAC,EAAE,IAAL;QAAWC,CAAC,EAAE;MAAd,CAAD,CAAtB;IAAA,CAAV;;IACAT,OAAO,CAACF,MAAR,UAAqB,mCAArB;;IAEAE,OAAO,GAAG;MAAA,OAAMT,gBAAgB,CAAC;QAAEiB,CAAC,EAAE,IAAL;QAAWE,SAAS,EAAE;MAAtB,CAAD,CAAtB;IAAA,CAAV;;IACAV,OAAO,CAACF,MAAR,UAAqB,2CAArB;;IAEAE,OAAO,GAAG;MAAA,OAAMT,gBAAgB,CAAC;QAAEoB,qBAAqB,EAAE,IAAzB;QAA+BD,SAAS,EAAE;MAA1C,CAAD,CAAtB;IAAA,CAAV;;IACAV,OAAO,CAACF,MAAR,UAAqB,wBAArB;;IAEAE,OAAO,GAAG;MAAA,OAAMT,gBAAgB,CAAC;QAAEoB,qBAAqB,EAAE,EAAzB;QAA6BD,SAAS,EAAE;MAAxC,CAAD,CAAtB;IAAA,CAAV;;IACAV,OAAO,CAACF,MAAR,UAAqB,wBAArB;IAEAP,gBAAgB,CAAC;MAAEoB,qBAAqB,EAAE,EAAzB;MAA6BD,SAAS,EAAE,EAAxC;MAA4CD,CAAC,EAAE;IAA/C,CAAD,CAAhB;EACA,CAvBC,CAAF;EAyBAd,EAAE,CAAC,2FAAD,EAA8F,YAAW;IAC1G,IAAMS,IAAI,GAAG,IAAId,QAAJ,CAAaD,UAAb,CAAb;IACAe,IAAI,CAACQ,mBAAL,CAAyB,KAAzB;IACA3B,IAAI,CAACmB,IAAI,CAACS,aAAL,EAAD,CAAJ,CAA2Bf,MAA3B,CAAkCC,KAAlC,CAAwC,QAAxC;EACA,CAJC,CAAF;EAMAJ,EAAE,CAAC,0EAAD,EAA6E,YAAW;IACzF,IAAMS,IAAI,GAAG,IAAId,QAAJ,CAAaF,UAAb,CAAb;IACAH,IAAI,CAACmB,IAAI,CAACH,wBAAL,CAA8B,KAA9B,CAAD,CAAJ,CAA2CH,MAA3C,CAAkDC,KAAlD,CAAwD,QAAxD;IACAd,IAAI,CAACmB,IAAI,CAACH,wBAAL,CAA8B,KAA9B,CAAD,CAAJ,CAA2CH,MAA3C,CAAkDC,KAAlD,CAAwD,WAAxD;EACA,CAJC,CAAF;EAMAJ,EAAE,CAAC,iDAAD,EAAoD,YAAW;IAChE,IAAMS,IAAI,GAAG,IAAId,QAAJ,CAAaH,UAAb,CAAb;IAEAiB,IAAI,CAACQ,mBAAL,CAAyB,IAAzB;IACAR,IAAI,CAACC,aAAL,CAAmBS,eAAnB,GAAqChB,MAArC,CAA4CiB,IAA5C,CAAiDhB,KAAjD,CAAuD,CAAC,EAAD,CAAvD;IACAK,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6BU,MAA7B,CAAoClB,MAApC,CAA2CC,KAA3C,CAAiD,CAAjD;IACAK,IAAI,CAACC,aAAL,CAAmBY,cAAnB,GAAoCnB,MAApC,CAA2CC,KAA3C,CAAiD,GAAjD;IACAK,IAAI,CAACC,aAAL,CAAmBH,wBAAnB,GAA8CJ,MAA9C,CAAqDC,KAArD,CAA2D,GAA3D;IACAK,IAAI,CAACC,aAAL,CAAmBpB,IAAnB,CAAwB,QAAxB,EAAkCiC,OAAlC,GAA4CpB,MAA5C,CAAmDC,KAAnD,CAAyD,EAAzD;IAEAK,IAAI,CAACQ,mBAAL,CAAyB,IAAzB;IACAR,IAAI,CAACC,aAAL,CAAmBc,aAAnB,GAAmCrB,MAAnC,CAA0CC,KAA1C,CAAgD,KAAhD,EAXgE,CAYhE;IACA;;IACAqB,MAAM,CAAChB,IAAI,CAACC,aAAL,CAAmBgB,2BAAnB,EAAD,CAAN,CAAyDC,EAAzD,CAA4DC,EAA5D;IAEAnB,IAAI,CAACQ,mBAAL,CAAyB,IAAzB;IACAR,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6B,CAA7B,EAAgCkB,4BAAhC,GAA+D1B,MAA/D,CAAsEC,KAAtE,CAA4E,KAA5E;IAEAK,IAAI,CAACQ,mBAAL,CAAyB,IAAzB;IACAR,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6B,CAA7B,EAAgCmB,sDAAhC,GAAyF3B,MAAzF,CAAgGC,KAAhG,CAAsG,IAAtG;EACA,CArBC,CAAF;EAuBAJ,EAAE,CAAC,gDAAD,EAAmD,YAAW;IAC/D,IAAMS,IAAI,GAAG,IAAId,QAAJ,CAAaJ,UAAb,CAAb;IAEAkB,IAAI,CAACQ,mBAAL,CAAyB,IAAzB;IACAR,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6BU,MAA7B,CAAoClB,MAApC,CAA2CC,KAA3C,CAAiD,CAAjD;IACAK,IAAI,CAACC,aAAL,CAAmBY,cAAnB,GAAoCnB,MAApC,CAA2CC,KAA3C,CAAiD,GAAjD;IACAK,IAAI,CAACC,aAAL,CAAmBH,wBAAnB,GAA8CJ,MAA9C,CAAqDC,KAArD,CAA2D,GAA3D;IACAd,IAAI,CAACmB,IAAI,CAACC,aAAL,CAAmBpB,IAAnB,CAAwB,QAAxB,CAAD,CAAJ,CAAwCa,MAAxC,CAA+CC,KAA/C,CAAqD,WAArD;IAEAK,IAAI,CAACQ,mBAAL,CAAyB,IAAzB;IACAR,IAAI,CAACC,aAAL,CAAmBc,aAAnB,GAAmCrB,MAAnC,CAA0CC,KAA1C,CAAgD,KAAhD,EAV+D,CAW/D;IACA;;IACAqB,MAAM,CAAChB,IAAI,CAACC,aAAL,CAAmBgB,2BAAnB,EAAD,CAAN,CAAyDC,EAAzD,CAA4DC,EAA5D;IAEAnB,IAAI,CAACQ,mBAAL,CAAyB,IAAzB;IACAR,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6B,CAA7B,EAAgCkB,4BAAhC,GAA+D1B,MAA/D,CAAsEC,KAAtE,CAA4E,KAA5E;IAEAK,IAAI,CAACQ,mBAAL,CAAyB,IAAzB;IACAR,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6B,CAA7B,EAAgCmB,sDAAhC,GAAyF3B,MAAzF,CAAgGC,KAAhG,CAAsG,IAAtG;EACA,CApBC,CAAF;EAsBAJ,EAAE,CAAC,4EAAD,EAA+E,YAAW;IAC3F,IAAMS,IAAI,GAAG,IAAId,QAAJ,CAAaJ,UAAb,CAAb;IACAkB,IAAI,CAACQ,mBAAL,CAAyB,IAAzB;IACAR,IAAI,CAACsB,GAAL,GAAW5B,MAAX,CAAkBC,KAAlB,CAAwB,QAAxB;IAEA,IAAM4B,OAAO,GAAG,IAAIrC,QAAJ,CAAaN,QAAb,CAAhB;IACA2C,OAAO,CAACf,mBAAR,CAA4B,IAA5B;IACAe,OAAO,CAACD,GAAR,GAAc5B,MAAd,CAAqBC,KAArB,CAA2B,IAA3B;EACA,CARC,CAAF;EAUAJ,EAAE,CAAC,2FAAD,EAA8F,YAAW;IAC1G,IAAMS,IAAI,GAAG,IAAId,QAAJ,CAAaJ,UAAb,CAAb;IACAkB,IAAI,CAACQ,mBAAL,CAAyB,IAAzB;IACA3B,IAAI,CAACmB,IAAI,CAACwB,gBAAL,EAAD,CAAJ,CAA8B9B,MAA9B,CAAqCC,KAArC,CAA2C,WAA3C;IAEA,IAAM4B,OAAO,GAAG,IAAIrC,QAAJ,CAAaN,QAAb,CAAhB;IACA2C,OAAO,CAACf,mBAAR,CAA4B,IAA5B;IACAe,OAAO,CAACC,gBAAR,GAA2B9B,MAA3B,CAAkCC,KAAlC,CAAwC,MAAxC;EACA,CARC,CAAF;AASA,CAtJO,CAAR;;AAwJA,SAASd,IAAT,CAAc4C,SAAd,EAAyB;EACxB,eAAcA,SAAd;AACA"}