<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户认证 - Global Timezone Spoofer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            width: 400px;
            height: 600px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: white;
            overflow: hidden;
        }

        .container {
            padding: 20px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 20px;
            margin-bottom: 5px;
        }

        .header .version {
            font-size: 12px;
            opacity: 0.8;
        }

        .auth-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .auth-tabs {
            display: flex;
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 4px;
        }

        .auth-tab {
            flex: 1;
            padding: 10px;
            text-align: center;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .auth-tab.active {
            background: rgba(255, 255, 255, 0.2);
            font-weight: 600;
        }

        .auth-form {
            display: none;
        }

        .auth-form.active {
            display: block;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 14px;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            background: white;
            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
        }

        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #45a049;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .message {
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-size: 13px;
            text-align: center;
            display: none;
        }

        .message.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.3);
            color: #4CAF50;
        }

        .message.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid rgba(244, 67, 54, 0.3);
            color: #f44336;
        }

        .message.warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.3);
            color: #FFC107;
        }

        .activation-section {
            margin-top: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            display: none;
        }

        .activation-section.show {
            display: block;
        }

        .user-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            display: none;
        }

        .user-info.show {
            display: block;
        }

        .user-info h3 {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .user-info p {
            font-size: 13px;
            margin-bottom: 5px;
            opacity: 0.9;
        }

        .purchase-link {
            color: #FFC107;
            text-decoration: none;
            font-weight: 600;
        }

        .purchase-link:hover {
            text-decoration: underline;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 过期提示样式 */
        .expired-notice {
            background: linear-gradient(135deg, #ff6b6b, #ff8e53);
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
            text-align: center;
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .notice-content {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .notice-icon {
            font-size: 32px;
            margin-bottom: 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .expired-notice h4 {
            margin: 0 0 10px 0;
            font-size: 18px;
            font-weight: bold;
        }

        .expired-notice p {
            margin: 0 0 15px 0;
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.4;
        }

        .purchase-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .purchase-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌍 Global Timezone Spoofer</h1>
            <div class="version">v2.9.7 - 用户认证</div>
        </div>

        <div class="auth-container">
            <!-- 消息显示 -->
            <div class="message" id="message"></div>

            <!-- 用户信息显示 -->
            <div class="user-info" id="user-info">
                <h3>👤 用户信息</h3>
                <p><strong>用户名:</strong> <span id="username-display"></span></p>
                <p><strong>剩余天数:</strong> <span id="remaining-days"></span> 天</p>
                <p><strong>账户状态:</strong> <span id="account-status"></span></p>

                <!-- 过期提示区域 -->
                <div id="expired-notice" class="expired-notice" style="display: none;">
                    <div class="notice-content">
                        <div class="notice-icon">⚠️</div>
                        <h4>账户已过期</h4>
                        <p>您的试用期已结束，请购买激活码继续使用</p>
                        <button id="purchase-button" class="purchase-btn">
                            🛒 立即购买激活码
                        </button>
                    </div>
                </div>
            </div>

            <!-- 认证标签页 -->
            <div class="auth-tabs">
                <div class="auth-tab active" data-tab="login">登录</div>
                <div class="auth-tab" data-tab="register">注册</div>
            </div>

            <!-- 登录表单 -->
            <div class="auth-form active" id="login-form">
                <div class="form-group">
                    <label for="login-username">用户名</label>
                    <input type="text" id="login-username" placeholder="请输入用户名" maxlength="20">
                </div>
                <div class="form-group">
                    <label for="login-password">密码</label>
                    <input type="password" id="login-password" placeholder="请输入密码">
                </div>
                <button class="btn btn-primary" id="login-btn">🔑 登录</button>
            </div>

            <!-- 注册表单 -->
            <div class="auth-form" id="register-form">
                <div class="form-group">
                    <label for="register-username">用户名</label>
                    <input type="text" id="register-username" placeholder="3-20个字符，仅限字母数字下划线" maxlength="20">
                </div>
                <div class="form-group">
                    <label for="register-password">密码</label>
                    <input type="password" id="register-password" placeholder="至少6个字符">
                </div>
                <div class="form-group">
                    <label for="register-confirm">确认密码</label>
                    <input type="password" id="register-confirm" placeholder="请再次输入密码">
                </div>
                <button class="btn btn-primary" id="register-btn">📝 注册</button>
            </div>

            <!-- 激活码部分 -->
            <div class="activation-section" id="activation-section">
                <h3>🎫 激活码</h3>
                <div class="form-group">
                    <label for="activation-code">激活码</label>
                    <input type="text" id="activation-code" placeholder="请输入激活码">
                </div>
                <button class="btn btn-secondary" id="activate-btn">✨ 激活</button>
                <p style="font-size: 12px; opacity: 0.8; margin-top: 10px;">
                    没有激活码？<a href="#" class="purchase-link" id="purchase-link">点击购买</a>
                </p>
            </div>

            <!-- 加载状态 -->
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <div>处理中...</div>
            </div>
        </div>
    </div>

    <script src="auth.js"></script>
</body>
</html>
