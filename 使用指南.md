# 插件授权系统使用指南

## 🚀 快速开始

### 1. 安装插件
1. 将插件文件夹加载到 Chrome 扩展程序中
2. 确保插件已启用
3. 点击插件图标开始使用

### 2. 用户登录
1. 首次使用会自动跳转到登录页面
2. 如果没有账户，先注册新用户
3. 输入用户名和密码登录
4. 系统会自动验证账户状态

### 3. 插件功能
- ✅ **已登录且未过期**: 插件正常工作，可以使用所有功能
- ❌ **未登录或已过期**: 插件功能被禁用，需要登录或激活

## 🔐 授权验证机制

### 工作流程
```
用户登录 → 状态验证 → 生成授权文件(ind.js) → 插件功能启用
```

### 验证层级
1. **ind.js 文件验证** - 主要验证机制
2. **Window 对象验证** - 内存中的授权状态
3. **LocalStorage 验证** - 本地存储的授权信息
4. **Chrome Storage 验证** - 扩展存储的授权数据

## 📋 功能说明

### 登录成功后
- 系统自动生成 `ind.js` 授权文件
- 插件功能在所有网页中启用
- 授权状态在所有标签页同步

### 账户过期时
- 授权文件被自动删除
- 插件功能立即停止工作
- 显示激活码输入界面

### 退出登录时
- 清除所有授权信息
- 删除授权文件
- 插件功能被禁用

## 🛠️ 测试方法

### 1. 使用测试页面
1. 打开 `test-auth.html` 文件
2. 查看授权状态检查结果
3. 测试插件功能是否正常

### 2. 控制台检查
1. 按 F12 打开开发者工具
2. 查看 Console 标签页
3. 寻找授权相关的日志信息：
   - `🔓 插件已授权` - 授权成功
   - `🔒 插件未授权` - 授权失败

### 3. 功能测试
- 检查时区是否正确伪装
- 验证地理位置是否已修改
- 确认语言设置是否生效

## ⚠️ 注意事项

### 重要提醒
1. **不要手动修改 ind.js 文件** - 它由系统自动管理
2. **授权有效期为24小时** - 超时需要重新验证
3. **退出登录会立即禁用功能** - 请谨慎操作
4. **账户过期后功能立即停止** - 需要激活码续费

### 安全特性
- 多层验证机制确保安全性
- 自动过期处理防止滥用
- 实时状态检查保证有效性
- 加密存储保护用户信息

## 🔧 故障排除

### 插件功能不工作
1. **检查登录状态**
   - 点击插件图标查看是否已登录
   - 确认账户是否过期

2. **清除缓存数据**
   - 清除浏览器缓存
   - 重新登录账户

3. **重新安装插件**
   - 移除插件
   - 重新加载插件文件夹

### 授权状态异常
1. **查看控制台日志**
   ```
   🔓 插件已授权 - 正常
   🔒 插件未授权 - 异常
   ❌ 检查授权状态时出错 - 错误
   ```

2. **手动清除授权数据**
   - 打开 `test-auth.html`
   - 点击"清除授权数据"按钮
   - 重新登录

3. **检查网络连接**
   - 确保能访问 http://103.96.75.196
   - 检查防火墙设置

## 📞 技术支持

如果遇到问题，请提供以下信息：
1. 浏览器版本和操作系统
2. 插件版本号
3. 控制台错误日志
4. 具体的问题描述

## 🔄 更新日志

### v2.9.7
- ✅ 实现完整的用户授权验证系统
- ✅ 添加 ind.js 授权标识文件
- ✅ 多层验证机制确保安全性
- ✅ 自动状态检查和过期处理
- ✅ 友好的用户界面和错误提示

---

**作者**: 小鱼游水  
**网址**: https://xoxome.online  
**版本**: v2.9.7
