<?php
require_once 'config.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, '只允许POST请求', null, 405);
}

// 获取输入数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    sendResponse(false, '无效的JSON数据', null, 400);
}

// 验证必需字段
$username = trim($input['username'] ?? '');
$password = trim($input['password'] ?? '');

if (empty($username) || empty($password)) {
    sendResponse(false, '用户名和密码不能为空', null, 400);
}

// 验证用户名格式
if (strlen($username) < 3 || strlen($username) > 20) {
    sendResponse(false, '用户名长度必须在3-20个字符之间', null, 400);
}

if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
    sendResponse(false, '用户名只能包含字母、数字和下划线', null, 400);
}

// 验证密码强度
if (strlen($password) < 6) {
    sendResponse(false, '密码长度至少6个字符', null, 400);
}

// 连接数据库
$pdo = getDBConnection();
if (!$pdo) {
    sendResponse(false, '数据库连接失败', null, 500);
}

try {
    // 检查用户名是否已存在
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->execute([$username]);
    if ($stmt->fetch()) {
        sendResponse(false, '用户名已存在', null, 409);
    }

    // 移除设备指纹限制 - 允许多设备使用

    // 获取默认试用天数
    $configValue = getSystemConfig($pdo, 'default_trial_days');
    $defaultTrialDays = ($configValue !== null) ? (int)$configValue : 7;

    // 计算到期时间
    $expiryDate = new DateTime();
    if ($defaultTrialDays > 0) {
        $expiryDate->add(new DateInterval("P{$defaultTrialDays}D"));
        $expiryDate->setTime(23, 59, 59); // 设置为当天的23:59:59
    } else {
        // 如果试用天数为0，设置为当前时间（立即过期）
        $expiryDate->setTime(0, 0, 0);
    }

    // 创建新用户
    $passwordHash = hashPassword($password);

    // 根据试用天数设置初始状态
    $initialStatus = ($defaultTrialDays > 0) ? 'active' : 'expired';

    $stmt = $pdo->prepare("
        INSERT INTO users (username, password_hash, expires_at, status)
        VALUES (?, ?, ?, ?)
    ");

    $stmt->execute([
        $username,
        $passwordHash,
        $expiryDate->format('Y-m-d H:i:s'),
        $initialStatus
    ]);

    $userId = $pdo->lastInsertId();

    // 记录注册日志
    logLogin($pdo, $userId, null, 'register');

    // 返回成功响应
    sendResponse(true, '注册成功', [
        'user_id' => $userId,
        'username' => $username,
        'expires_at' => $expiryDate->format('Y-m-d H:i:s'),
        'remaining_days' => $defaultTrialDays,
        'status' => $initialStatus
    ]);

} catch (PDOException $e) {
    error_log("Registration error: " . $e->getMessage());
    sendResponse(false, '注册失败，请稍后重试', null, 500);
} catch (Exception $e) {
    error_log("Registration error: " . $e->getMessage());
    sendResponse(false, '注册过程中发生错误', null, 500);
}
?>
