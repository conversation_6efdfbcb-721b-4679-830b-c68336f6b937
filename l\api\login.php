<?php
require_once 'config.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, '只允许POST请求', null, 405);
}

// 获取输入数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    sendResponse(false, '无效的JSON数据', null, 400);
}

// 验证必需字段
$username = trim($input['username'] ?? '');
$password = trim($input['password'] ?? '');

if (empty($username) || empty($password)) {
    sendResponse(false, '用户名和密码不能为空', null, 400);
}

// 连接数据库
$pdo = getDBConnection();
if (!$pdo) {
    sendResponse(false, '数据库连接失败', null, 500);
}

try {
    // 查找用户
    $stmt = $pdo->prepare("
        SELECT id, username, password_hash, expires_at, status, last_login, created_at
        FROM users
        WHERE username = ?
    ");
    $stmt->execute([$username]);
    $user = $stmt->fetch();

    if (!$user) {
        logLogin($pdo, null, null, 'failed');
        sendResponse(false, '用户名或密码错误', null, 401);
    }

    // 验证密码
    if (!verifyPassword($password, $user['password_hash'])) {
        logLogin($pdo, $user['id'], null, 'failed');
        sendResponse(false, '用户名或密码错误', null, 401);
    }

    // 检查账户状态 - 如果是过期状态，需要返回用户数据以显示过期界面
    if ($user['status'] === 'suspended') {
        logLogin($pdo, $user['id'], null, 'failed');
        sendResponse(false, '账户已被暂停，请联系客服', null, 403);
    }

    // 移除设备指纹验证 - 允许多设备登录

    // 检查用户是否过期（检查时间和状态）
    $isExpired = false;

    // 检查数据库状态
    if ($user['status'] === 'expired') {
        $isExpired = true;
    }
    // 检查到期时间
    else if ($user['expires_at']) {
        $currentTime = new DateTime();
        $expiryTime = new DateTime($user['expires_at']);
        $isExpired = $currentTime >= $expiryTime;
    } else {
        $isExpired = true; // 没有到期时间视为过期
    }

    if ($isExpired) {
        // 更新用户状态为过期
        $stmt = $pdo->prepare("UPDATE users SET status = 'expired' WHERE id = ?");
        $stmt->execute([$user['id']]);

        logLogin($pdo, $user['id'], null, 'failed');

        // 获取购买链接
        $purchaseLink = getSystemConfig($pdo, 'purchase_link');

        // 返回完整的用户数据以便前端显示过期界面
        sendResponse(false, '账户已过期，请购买激活码继续使用', [
            'expired' => true,
            'purchase_link' => $purchaseLink,
            'remaining_days' => 0,
            'user_data' => [
                'user_id' => $user['id'],
                'username' => $user['username'],
                'expires_at' => $user['expires_at'],
                'created_at' => $user['created_at'],
                'status' => 'expired',
                'purchase_link' => $purchaseLink
            ]
        ], 402);
    }

    // 更新最后登录时间
    $stmt = $pdo->prepare("
        UPDATE users
        SET last_login = NOW()
        WHERE id = ?
    ");
    $stmt->execute([$user['id']]);

    // 记录成功登录
    logLogin($pdo, $user['id'], null, 'success');

    // 获取系统配置
    $purchaseLink = getSystemConfig($pdo, 'purchase_link');

    // 计算剩余天数用于显示（简化版本）
    $remainingDays = 0;
    if ($user['expires_at']) {
        $currentTime = new DateTime();
        $expiryTime = new DateTime($user['expires_at']);
        if ($currentTime < $expiryTime) {
            $diff = $currentTime->diff($expiryTime);
            $remainingDays = $diff->days;
        }
    }

    // 返回成功响应
    sendResponse(true, '登录成功', [
        'user_id' => $user['id'],
        'username' => $user['username'],
        'expires_at' => $user['expires_at'],
        'remaining_days' => $remainingDays,
        'status' => $user['status'],
        'last_login' => $user['last_login'],
        'purchase_link' => $purchaseLink
    ]);

} catch (PDOException $e) {
    error_log("Login error: " . $e->getMessage());
    sendResponse(false, '登录失败，请稍后重试', null, 500);
} catch (Exception $e) {
    error_log("Login error: " . $e->getMessage());
    sendResponse(false, '登录过程中发生错误', null, 500);
}
?>
