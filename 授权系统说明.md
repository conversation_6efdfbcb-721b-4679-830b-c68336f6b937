# 插件授权系统说明

## 概述

本插件实现了一个完整的用户登录验证系统，只有用户登录且没有过期的情况下才能让插件功能生效。

## 工作原理

### 1. 授权文件管理

- **ind.js 文件**: 作为授权标识文件，包含授权验证逻辑
- **background.js**: 负责管理授权状态，生成和删除授权信息
- **content.js**: 在页面加载时检查授权状态，决定是否启用插件功能

### 2. 授权验证流程

1. **用户登录**: 用户在 auth.html 页面登录
2. **状态验证**: 系统验证用户状态（剩余天数、激活状态）
3. **授权生成**: 如果用户有效，background.js 生成授权信息
4. **功能启用**: content.js 检测到授权后启用插件功能
5. **定期检查**: 每30分钟自动检查一次用户状态

### 3. 多层验证机制

插件使用多层验证机制确保安全性：

#### 第一层：ind.js 验证函数
- 使用 `window.verifyPluginAuth()` 函数
- 检查授权状态、用户信息完整性、授权时间有效性

#### 第二层：Window 对象验证
- 检查 `window.PLUGIN_AUTHORIZED` 标记
- 验证 `window.PLUGIN_USER_INFO` 用户信息

#### 第三层：LocalStorage 验证
- 检查 `plugin_auth_status` 状态
- 验证 `plugin_user_info` 用户数据
- 检查授权时间（24小时内有效）

#### 第四层：Chrome Storage 验证
- 异步检查扩展存储中的授权状态
- 验证 `plugin_authorized`、`auth_user_info`、`ind_file_status`

### 4. 授权状态管理

#### 授权生成时机：
- 用户成功登录且状态为 active
- 用户成功激活且剩余天数 > 0
- 定期检查发现用户状态有效

#### 授权删除时机：
- 用户退出登录
- 用户状态过期或无效
- 认证检查失败

### 5. 文件结构

```
├── ind.js              # 授权标识文件（包含验证逻辑）
├── background.js       # 后台脚本（管理授权状态）
├── content.js          # 内容脚本（检查授权并启用功能）
├── popup.js           # 弹窗脚本（用户界面）
├── auth.js            # 认证脚本（登录/注册）
├── test-auth.html     # 授权测试页面
└── manifest.json      # 插件配置文件
```

## 使用说明

### 1. 用户登录
1. 点击插件图标
2. 如果未登录，会自动跳转到认证页面
3. 输入用户名和密码登录
4. 系统验证用户状态并生成授权

### 2. 插件功能
- 只有授权用户才能使用插件的时区和地理位置伪装功能
- 未授权用户访问任何网页时，插件功能都不会生效
- 授权状态会在所有标签页中同步

### 3. 授权过期
- 用户账户过期时，授权会被自动删除
- 用户可以通过输入激活码重新激活
- 授权信息24小时内有效，超时需要重新验证

## 技术特点

### 1. 安全性
- 多层验证机制
- 授权信息加密存储
- 定期状态检查
- 自动过期处理

### 2. 可靠性
- 多种存储方式备份
- 异步状态同步
- 错误处理机制
- 自动恢复功能

### 3. 用户体验
- 无感知授权验证
- 自动状态同步
- 友好的过期提示
- 简单的激活流程

## 测试方法

1. 打开 `test-auth.html` 页面
2. 查看授权状态检查结果
3. 测试插件功能是否正常
4. 验证授权数据的完整性

## 注意事项

1. **不要手动修改 ind.js 文件**，它由系统自动管理
2. **授权信息会在24小时后过期**，需要重新验证
3. **退出登录会立即删除所有授权信息**
4. **插件功能只在授权有效时才会启用**

## 故障排除

### 插件功能不生效
1. 检查是否已登录
2. 验证账户是否过期
3. 查看浏览器控制台的授权日志
4. 尝试重新登录

### 授权状态异常
1. 清除浏览器缓存和存储
2. 重新安装插件
3. 重新登录账户
4. 联系技术支持
