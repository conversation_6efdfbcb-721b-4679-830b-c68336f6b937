<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扩展诊断工具 - Extension Diagnostics</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .diagnostic-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .diagnostic-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .diagnostic-card h3 {
            margin-top: 0;
            color: #fff;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .result {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            margin: 8px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            border-left: 3px solid #4CAF50;
        }
        
        .warning {
            border-left-color: #ff9800;
            background: rgba(255, 152, 0, 0.1);
        }
        
        .error {
            border-left-color: #f44336;
            background: rgba(244, 67, 54, 0.1);
        }
        
        .success {
            border-left-color: #4CAF50;
            background: rgba(76, 175, 80, 0.1);
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        
        .summary {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        
        .summary h3 {
            margin-top: 0;
            color: #2196F3;
        }
        
        .issue-count {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }
        
        .count-item {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            min-width: 100px;
        }
        
        .count-success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
        }
        
        .count-warning {
            background: rgba(255, 152, 0, 0.2);
            border: 1px solid #ff9800;
        }
        
        .count-error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
        }
        
        .recommendations {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 扩展诊断工具</h1>
        <h2>Extension Diagnostics & Issue Detection</h2>
        <p>检测扩展可能导致注册被拒绝的问题</p>
    </div>
    
    <div class="summary" id="summary">
        <h3>📊 诊断摘要</h3>
        <div class="issue-count">
            <div class="count-item count-success">
                <div style="font-size: 24px; font-weight: bold;" id="success-count">0</div>
                <div>正常项目</div>
            </div>
            <div class="count-item count-warning">
                <div style="font-size: 24px; font-weight: bold;" id="warning-count">0</div>
                <div>警告项目</div>
            </div>
            <div class="count-item count-error">
                <div style="font-size: 24px; font-weight: bold;" id="error-count">0</div>
                <div>错误项目</div>
            </div>
        </div>
        <div id="overall-status"></div>
    </div>
    
    <div class="diagnostic-grid">
        <div class="diagnostic-card">
            <h3>🌍 地理位置一致性检查</h3>
            <button class="btn" onclick="checkGeolocationConsistency()">开始检查</button>
            <div id="geolocation-results"></div>
        </div>
        
        <div class="diagnostic-card">
            <h3>🕐 时区一致性检查</h3>
            <button class="btn" onclick="checkTimezoneConsistency()">开始检查</button>
            <div id="timezone-results"></div>
        </div>
        
        <div class="diagnostic-card">
            <h3>🗣️ 语言设置检查</h3>
            <button class="btn" onclick="checkLanguageSettings()">开始检查</button>
            <div id="language-results"></div>
        </div>
        
        <div class="diagnostic-card">
            <h3>🌐 网络指纹检查</h3>
            <button class="btn" onclick="checkNetworkFingerprint()">开始检查</button>
            <div id="network-results"></div>
        </div>
        
        <div class="diagnostic-card">
            <h3>🖥️ 设备指纹检查</h3>
            <button class="btn" onclick="checkDeviceFingerprint()">开始检查</button>
            <div id="device-results"></div>
        </div>
        
        <div class="diagnostic-card">
            <h3>🔍 扩展功能检查</h3>
            <button class="btn" onclick="checkExtensionFunctionality()">开始检查</button>
            <div id="extension-results"></div>
        </div>
    </div>
    
    <div class="recommendations" id="recommendations" style="display: none;">
        <h3>💡 修复建议</h3>
        <div id="recommendation-list"></div>
    </div>
    
    <script>
        let diagnosticResults = {
            success: 0,
            warning: 0,
            error: 0,
            issues: []
        };
        
        function addResult(containerId, content, type = 'result') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = content;
            container.appendChild(div);
            
            // 更新统计
            if (type === 'success') diagnosticResults.success++;
            else if (type === 'warning') {
                diagnosticResults.warning++;
                diagnosticResults.issues.push(content);
            }
            else if (type === 'error') {
                diagnosticResults.error++;
                diagnosticResults.issues.push(content);
            }
            
            updateSummary();
        }
        
        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }
        
        function updateSummary() {
            document.getElementById('success-count').textContent = diagnosticResults.success;
            document.getElementById('warning-count').textContent = diagnosticResults.warning;
            document.getElementById('error-count').textContent = diagnosticResults.error;
            
            const totalIssues = diagnosticResults.warning + diagnosticResults.error;
            const statusDiv = document.getElementById('overall-status');
            
            if (totalIssues === 0) {
                statusDiv.innerHTML = '<div style="color: #4CAF50; font-weight: bold;">✅ 扩展运行正常，未发现问题</div>';
            } else if (totalIssues <= 2) {
                statusDiv.innerHTML = '<div style="color: #ff9800; font-weight: bold;">⚠️ 发现少量问题，可能影响注册成功率</div>';
            } else {
                statusDiv.innerHTML = '<div style="color: #f44336; font-weight: bold;">❌ 发现多个问题，注册很可能被拒绝</div>';
            }
            
            if (totalIssues > 0) {
                showRecommendations();
            }
        }
        
        function showRecommendations() {
            document.getElementById('recommendations').style.display = 'block';
            const recommendationList = document.getElementById('recommendation-list');
            
            let recommendations = [];
            
            if (diagnosticResults.issues.some(issue => issue.includes('时区'))) {
                recommendations.push('• 检查扩展的时区设置，确保选择了正确的美国时区');
                recommendations.push('• 重新打开扩展设置面板，选择美国地区并应用');
            }
            
            if (diagnosticResults.issues.some(issue => issue.includes('地理位置') || issue.includes('GPS'))) {
                recommendations.push('• 检查地理位置权限是否正确授予');
                recommendations.push('• 确保扩展的地理位置伪装功能正常工作');
            }
            
            if (diagnosticResults.issues.some(issue => issue.includes('语言'))) {
                recommendations.push('• 检查浏览器语言设置，确保设置为en-US');
                recommendations.push('• 重启浏览器以确保语言设置生效');
            }
            
            if (diagnosticResults.issues.some(issue => issue.includes('IP'))) {
                recommendations.push('• 检查是否使用了VPN或代理服务');
                recommendations.push('• 考虑使用美国住宅IP地址');
            }
            
            if (recommendations.length === 0) {
                recommendations.push('• 尝试重新安装扩展');
                recommendations.push('• 清除浏览器缓存和Cookie');
                recommendations.push('• 使用隐私模式重新测试');
            }
            
            recommendationList.innerHTML = recommendations.join('<br>');
        }
        
        function checkGeolocationConsistency() {
            clearResults('geolocation-results');
            addResult('geolocation-results', '🔍 检查地理位置一致性...', 'result');
            
            // 检查扩展设置
            const savedSettings = localStorage.getItem('globalTimezoneSpoofer_selectedCity');
            if (savedSettings) {
                try {
                    const cityData = JSON.parse(savedSettings);
                    addResult('geolocation-results', `✅ 扩展设置: ${cityData.city}, ${cityData.country}`, 'success');
                    
                    if (cityData.country_code === 'US') {
                        addResult('geolocation-results', '✅ 扩展配置为美国地区', 'success');
                    } else {
                        addResult('geolocation-results', `❌ 扩展配置为非美国地区: ${cityData.country}`, 'error');
                    }
                } catch (e) {
                    addResult('geolocation-results', '❌ 扩展设置解析失败', 'error');
                }
            } else {
                addResult('geolocation-results', '⚠️ 未找到扩展设置，可能使用随机地区', 'warning');
            }
            
            // 检查GPS位置
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;
                        
                        if (lat >= 24.396308 && lat <= 49.384358 && lng >= -125.0 && lng <= -66.93457) {
                            addResult('geolocation-results', `✅ GPS位置在美国: ${lat.toFixed(4)}, ${lng.toFixed(4)}`, 'success');
                        } else {
                            addResult('geolocation-results', `❌ GPS位置不在美国: ${lat.toFixed(4)}, ${lng.toFixed(4)}`, 'error');
                        }
                        
                        if (position.coords.accuracy > 1000) {
                            addResult('geolocation-results', `⚠️ GPS精度较低: ${position.coords.accuracy}米`, 'warning');
                        } else {
                            addResult('geolocation-results', `✅ GPS精度正常: ${position.coords.accuracy}米`, 'success');
                        }
                    },
                    (error) => {
                        addResult('geolocation-results', `❌ GPS位置获取失败: ${error.message}`, 'error');
                    }
                );
            } else {
                addResult('geolocation-results', '❌ 地理位置API不可用', 'error');
            }
            
            // 检查IP地理位置
            fetch('https://ipapi.co/json/')
                .then(response => response.json())
                .then(data => {
                    if (data.country_code === 'US') {
                        addResult('geolocation-results', `✅ IP地理位置: ${data.city}, ${data.region}, US`, 'success');
                    } else {
                        addResult('geolocation-results', `❌ IP地理位置不在美国: ${data.country_name}`, 'error');
                    }
                    
                    if (data.org && (data.org.toLowerCase().includes('vpn') || 
                                   data.org.toLowerCase().includes('proxy') ||
                                   data.org.toLowerCase().includes('hosting'))) {
                        addResult('geolocation-results', `⚠️ 检测到VPN/代理: ${data.org}`, 'warning');
                    }
                })
                .catch(error => {
                    addResult('geolocation-results', '⚠️ 无法获取IP地理位置', 'warning');
                });
        }
        
        function checkTimezoneConsistency() {
            clearResults('timezone-results');
            addResult('timezone-results', '🔍 检查时区一致性...', 'result');
            
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            const offset = new Date().getTimezoneOffset();
            
            addResult('timezone-results', `🕐 当前时区: ${timezone}`, 'result');
            addResult('timezone-results', `⏰ 时区偏移: ${offset} 分钟`, 'result');
            
            const usTimezones = [
                'America/New_York', 'America/Chicago', 'America/Denver', 
                'America/Los_Angeles', 'America/Phoenix', 'America/Anchorage',
                'Pacific/Honolulu'
            ];
            
            if (usTimezones.includes(timezone)) {
                addResult('timezone-results', '✅ 时区设置正确 (美国时区)', 'success');
            } else {
                addResult('timezone-results', `❌ 时区不是美国时区: ${timezone}`, 'error');
            }
            
            // 检查时间一致性
            const now = new Date();
            const localTime = now.toLocaleString();
            const utcTime = now.toUTCString();
            
            addResult('timezone-results', `🌍 本地时间: ${localTime}`, 'result');
            addResult('timezone-results', `🌐 UTC时间: ${utcTime}`, 'result');
            
            // 检查Date.now()一致性
            const dateNow = Date.now();
            const newDateGetTime = new Date().getTime();
            const timeDiff = Math.abs(dateNow - newDateGetTime);
            
            if (timeDiff < 100) {
                addResult('timezone-results', '✅ Date.now()与new Date().getTime()一致', 'success');
            } else {
                addResult('timezone-results', `⚠️ 时间API不一致，差异: ${timeDiff}ms`, 'warning');
            }
        }
        
        function checkLanguageSettings() {
            clearResults('language-results');
            addResult('language-results', '🔍 检查语言设置...', 'result');
            
            const language = navigator.language;
            const languages = navigator.languages;
            
            addResult('language-results', `🗣️ 主语言: ${language}`, 'result');
            addResult('language-results', `🌐 支持语言: ${languages.join(', ')}`, 'result');
            
            if (language.startsWith('en-US')) {
                addResult('language-results', '✅ 主语言设置正确 (en-US)', 'success');
            } else {
                addResult('language-results', `❌ 主语言不是en-US: ${language}`, 'error');
            }
            
            if (languages[0] === 'en-US') {
                addResult('language-results', '✅ 首选语言正确', 'success');
            } else {
                addResult('language-results', `⚠️ 首选语言不是en-US: ${languages[0]}`, 'warning');
            }
            
            // 检查数字和日期格式
            const number = 1234567.89;
            const date = new Date();
            
            const numberFormat = number.toLocaleString();
            const dateFormat = date.toLocaleDateString();
            
            addResult('language-results', `🔢 数字格式: ${numberFormat}`, 'result');
            addResult('language-results', `📅 日期格式: ${dateFormat}`, 'result');
        }
        
        function checkNetworkFingerprint() {
            clearResults('network-results');
            addResult('network-results', '🔍 检查网络指纹...', 'result');
            
            const userAgent = navigator.userAgent;
            addResult('network-results', `🖥️ User-Agent: ${userAgent.substring(0, 80)}...`, 'result');
            
            if (userAgent.includes('Windows') || userAgent.includes('Macintosh') || userAgent.includes('Linux')) {
                addResult('network-results', '✅ User-Agent看起来正常', 'success');
            } else {
                addResult('network-results', '⚠️ User-Agent可能异常', 'warning');
            }
            
            // 检查HTTP头
            fetch('https://httpbin.org/headers')
                .then(response => response.json())
                .then(data => {
                    const headers = data.headers;
                    const acceptLanguage = headers['Accept-Language'];
                    
                    addResult('network-results', `📡 Accept-Language: ${acceptLanguage}`, 'result');
                    
                    if (acceptLanguage && acceptLanguage.includes('en-US')) {
                        addResult('network-results', '✅ HTTP语言头正确', 'success');
                    } else {
                        addResult('network-results', '❌ HTTP语言头可能暴露真实位置', 'error');
                    }
                })
                .catch(error => {
                    addResult('network-results', '⚠️ 无法检查HTTP头', 'warning');
                });
        }
        
        function checkDeviceFingerprint() {
            clearResults('device-results');
            addResult('device-results', '🔍 检查设备指纹...', 'result');
            
            const screenWidth = screen.width;
            const screenHeight = screen.height;
            const resolution = `${screenWidth}x${screenHeight}`;
            
            addResult('device-results', `📺 屏幕分辨率: ${resolution}`, 'result');
            
            const commonResolutions = [
                '1920x1080', '1366x768', '1440x900', '1536x864', 
                '1280x720', '2560x1440', '1600x900'
            ];
            
            if (commonResolutions.includes(resolution)) {
                addResult('device-results', '✅ 屏幕分辨率常见', 'success');
            } else {
                addResult('device-results', '⚠️ 屏幕分辨率不常见', 'warning');
            }
            
            const hardwareConcurrency = navigator.hardwareConcurrency;
            addResult('device-results', `💻 CPU核心数: ${hardwareConcurrency}`, 'result');
            
            if (hardwareConcurrency >= 4 && hardwareConcurrency <= 16) {
                addResult('device-results', '✅ CPU核心数正常', 'success');
            } else {
                addResult('device-results', '⚠️ CPU核心数可能异常', 'warning');
            }
            
            if (navigator.deviceMemory) {
                addResult('device-results', `🧠 设备内存: ${navigator.deviceMemory}GB`, 'result');
            }
        }
        
        function checkExtensionFunctionality() {
            clearResults('extension-results');
            addResult('extension-results', '🔍 检查扩展功能...', 'result');
            
            // 检查扩展是否正在运行
            const extensionMarker = document.querySelector('script[data-extension="timezone-spoofer"]');
            if (extensionMarker || window.globalTimezoneSpooferActive) {
                addResult('extension-results', '✅ 扩展正在运行', 'success');
            } else {
                addResult('extension-results', '❌ 扩展可能未正确加载', 'error');
            }
            
            // 检查localStorage设置
            const savedSettings = localStorage.getItem('globalTimezoneSpoofer_selectedCity');
            if (savedSettings) {
                addResult('extension-results', '✅ 找到扩展设置', 'success');
            } else {
                addResult('extension-results', '⚠️ 未找到扩展设置', 'warning');
            }
            
            // 检查控制台输出
            const originalConsoleLog = console.log;
            let extensionLogs = [];
            
            console.log = function(...args) {
                const message = args.join(' ');
                if (message.includes('Global Timezone') || message.includes('🌍')) {
                    extensionLogs.push(message);
                }
                return originalConsoleLog.apply(console, args);
            };
            
            setTimeout(() => {
                if (extensionLogs.length > 0) {
                    addResult('extension-results', `✅ 检测到扩展日志: ${extensionLogs.length}条`, 'success');
                } else {
                    addResult('extension-results', '⚠️ 未检测到扩展日志', 'warning');
                }
                console.log = originalConsoleLog;
            }, 1000);
        }
        
        // 页面加载时重置统计
        window.addEventListener('load', () => {
            diagnosticResults = { success: 0, warning: 0, error: 0, issues: [] };
        });
        
        // 自动运行所有检查
        function runAllDiagnostics() {
            diagnosticResults = { success: 0, warning: 0, error: 0, issues: [] };
            
            checkGeolocationConsistency();
            setTimeout(() => checkTimezoneConsistency(), 500);
            setTimeout(() => checkLanguageSettings(), 1000);
            setTimeout(() => checkNetworkFingerprint(), 1500);
            setTimeout(() => checkDeviceFingerprint(), 2000);
            setTimeout(() => checkExtensionFunctionality(), 2500);
        }
        
        // 添加自动运行按钮
        const header = document.querySelector('.header');
        const autoRunBtn = document.createElement('button');
        autoRunBtn.className = 'btn';
        autoRunBtn.textContent = '🚀 运行完整诊断';
        autoRunBtn.onclick = runAllDiagnostics;
        header.appendChild(autoRunBtn);
    </script>
</body>
</html>
