<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Global Timezone & Location Spoofer Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .result {
            background: #f0f8ff;
            padding: 10px;
            border-left: 4px solid #007acc;
            margin: 10px 0;
            font-family: monospace;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .error {
            background: #ffe6e6;
            border-left-color: #ff4444;
            color: #cc0000;
        }
    </style>
</head>
<body>
    <h1>🌍 Global Timezone & Location Spoofer Test Page</h1>
    
    <div class="test-section">
        <h2>📅 Date & Time Tests</h2>
        <button onclick="testDateTime()">Test Date & Time</button>
        <div id="datetime-results"></div>
    </div>

    <div class="test-section">
        <h2>🌐 Timezone Tests</h2>
        <button onclick="testTimezone()">Test Timezone</button>
        <div id="timezone-results"></div>
    </div>

    <div class="test-section">
        <h2>📍 Geolocation Tests</h2>
        <button onclick="testGeolocation()">Test Geolocation</button>
        <div id="geolocation-results"></div>
    </div>

    <div class="test-section">
        <h2>🗣️ Language & Locale Tests</h2>
        <button onclick="testLanguage()">Test Language</button>
        <div id="language-results"></div>
    </div>

    <div class="test-section">
        <h2>🖥️ Browser Info Tests</h2>
        <button onclick="testBrowserInfo()">Test Browser Info</button>
        <div id="browser-results"></div>
    </div>

    <script>
        function addResult(containerId, content, isError = false) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = isError ? 'result error' : 'result';
            div.innerHTML = content;
            container.appendChild(div);
        }

        function testDateTime() {
            const container = document.getElementById('datetime-results');
            container.innerHTML = '';
            
            try {
                const now = new Date();
                addResult('datetime-results', `Current Date: ${now.toString()}`);
                addResult('datetime-results', `UTC String: ${now.toUTCString()}`);
                addResult('datetime-results', `ISO String: ${now.toISOString()}`);
                addResult('datetime-results', `Locale String: ${now.toLocaleString()}`);
                addResult('datetime-results', `Date.now(): ${Date.now()}`);
                addResult('datetime-results', `Timezone Offset: ${now.getTimezoneOffset()} minutes`);
            } catch (e) {
                addResult('datetime-results', `Error: ${e.message}`, true);
            }
        }

        function testTimezone() {
            const container = document.getElementById('timezone-results');
            container.innerHTML = '';
            
            try {
                const formatter = new Intl.DateTimeFormat();
                const options = formatter.resolvedOptions();
                addResult('timezone-results', `Resolved Timezone: ${options.timeZone}`);
                addResult('timezone-results', `Resolved Locale: ${options.locale}`);
                
                const now = new Date();
                const timeInTimezone = now.toLocaleString('en-US', {timeZone: options.timeZone});
                addResult('timezone-results', `Time in Timezone: ${timeInTimezone}`);
            } catch (e) {
                addResult('timezone-results', `Error: ${e.message}`, true);
            }
        }

        function testGeolocation() {
            const container = document.getElementById('geolocation-results');
            container.innerHTML = '';
            
            if (!navigator.geolocation) {
                addResult('geolocation-results', 'Geolocation not supported', true);
                return;
            }

            navigator.geolocation.getCurrentPosition(
                (position) => {
                    addResult('geolocation-results', `Latitude: ${position.coords.latitude}`);
                    addResult('geolocation-results', `Longitude: ${position.coords.longitude}`);
                    addResult('geolocation-results', `Accuracy: ${position.coords.accuracy} meters`);
                    addResult('geolocation-results', `Timestamp: ${new Date(position.timestamp).toString()}`);
                },
                (error) => {
                    addResult('geolocation-results', `Geolocation Error: ${error.message}`, true);
                },
                { timeout: 10000, maximumAge: 0 }
            );
        }

        function testLanguage() {
            const container = document.getElementById('language-results');
            container.innerHTML = '';
            
            try {
                addResult('language-results', `Navigator Language: ${navigator.language}`);
                addResult('language-results', `Navigator Languages: ${navigator.languages.join(', ')}`);
                
                const number = 1234567.89;
                addResult('language-results', `Number Locale String: ${number.toLocaleString()}`);
                
                const date = new Date();
                addResult('language-results', `Date Locale String: ${date.toLocaleDateString()}`);
                addResult('language-results', `Time Locale String: ${date.toLocaleTimeString()}`);
            } catch (e) {
                addResult('language-results', `Error: ${e.message}`, true);
            }
        }

        function testBrowserInfo() {
            const container = document.getElementById('browser-results');
            container.innerHTML = '';
            
            try {
                addResult('browser-results', `User Agent: ${navigator.userAgent}`);
                addResult('browser-results', `Platform: ${navigator.platform}`);
                addResult('browser-results', `Hardware Concurrency: ${navigator.hardwareConcurrency || 'N/A'}`);
                addResult('browser-results', `Screen Resolution: ${screen.width}x${screen.height}`);
                addResult('browser-results', `Available Resolution: ${screen.availWidth}x${screen.availHeight}`);
                addResult('browser-results', `Color Depth: ${screen.colorDepth}`);
                addResult('browser-results', `Pixel Depth: ${screen.pixelDepth}`);
                
                if (navigator.connection) {
                    addResult('browser-results', `Connection Type: ${navigator.connection.effectiveType || 'N/A'}`);
                }
            } catch (e) {
                addResult('browser-results', `Error: ${e.message}`, true);
            }
        }

        // Auto-run all tests on page load
        window.addEventListener('load', () => {
            console.log('🧪 Running automatic tests...');
            setTimeout(() => {
                testDateTime();
                testTimezone();
                testGeolocation();
                testLanguage();
                testBrowserInfo();
            }, 1000);
        });
    </script>
</body>
</html>
