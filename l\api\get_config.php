<?php
// 获取系统配置API
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-Custom-Header');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许GET请求']);
    exit();
}

require_once 'config.php';

try {
    // 获取配置键名
    $configKey = $_GET['key'] ?? '';
    
    if (empty($configKey)) {
        sendResponse(false, '配置键名不能为空', null, 400);
    }
    
    // 连接数据库
    $pdo = getDBConnection();
    if (!$pdo) {
        sendResponse(false, '数据库连接失败', null, 500);
    }
    
    // 查询配置
    $stmt = $pdo->prepare("SELECT config_key, config_value, description FROM system_config WHERE config_key = ?");
    $stmt->execute([$configKey]);
    $config = $stmt->fetch();
    
    if (!$config) {
        sendResponse(false, '配置不存在', null, 404);
    }
    
    // 返回配置数据
    sendResponse(true, '获取配置成功', $config);
    
} catch (Exception $e) {
    error_log("Get config error: " . $e->getMessage());
    sendResponse(false, '获取配置失败', null, 500);
}
?>
