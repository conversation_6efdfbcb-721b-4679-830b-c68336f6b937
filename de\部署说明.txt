🚀 API部署说明 - Timezone Extension

服务器IP: *************

📁 部署步骤：

1. 将整个 api 文件夹上传到您的服务器 PHP 站点根目录
   例如：/var/www/html/api/ 或 /public_html/api/

2. 确保服务器支持：
   - PHP 7.4 或更高版本
   - MySQL 数据库
   - PDO MySQL 扩展
   - 允许跨域请求 (CORS)

3. 文件权限设置：
   - 所有 .php 文件设置为 644 权限
   - api 文件夹设置为 755 权限

4. 数据库配置已设置为：
   - 主机: mail.xoxome.online
   - 数据库: timezone
   - 用户: timezone
   - 密码: M3Nbnni46HndHhzE

📋 API端点列表：

✅ http://*************/api/test.php - API测试页面
✅ http://*************/api/register.php - 用户注册
✅ http://*************/api/login.php - 用户登录
✅ http://*************/api/activate.php - 激活码激活
✅ http://*************/api/user_status.php - 用户状态查询

🔧 测试方法：

1. 首先访问测试页面：
   http://*************/api/test.php
   
2. 检查所有测试项目是否通过：
   - 数据库连接测试
   - 系统配置测试
   - API端点测试
   - 设备指纹生成测试
   - 密码哈希测试
   - 客户端IP获取测试

🌐 CORS配置：

API已配置为允许所有域名跨域访问：
- Access-Control-Allow-Origin: *
- Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
- Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-Custom-Header

🔒 安全特性：

- bcrypt密码哈希 (cost=12)
- 多设备登录支持 (已移除设备限制)
- SQL注入防护 (PDO预处理语句)
- 输入验证和过滤
- 错误日志记录

📱 插件配置：

插件已更新为使用您的服务器IP：
- API_BASE: http://*************/api
- manifest.json 已添加相应权限

🎯 使用流程：

1. 用户打开插件 → 跳转到认证页面
2. 注册新账户 → 获得1天试用期
3. 登录验证 → 检查剩余天数 (支持多设备登录)
4. 输入激活码 → 延长使用期限
5. 过期用户 → 显示购买提示界面，引导购买激活码

💰 购买提示功能：

- 剩余天数为0时自动显示购买提示
- 美观的过期提示界面，包含购买按钮
- 购买地址来自数据库 system_config 表的 purchase_link 配置
- 支持在popup和auth页面显示购买提示
- 提供多种操作选项：购买激活码、重新登录、输入激活码、联系客服

🎨 增强的过期界面：

- 显示用户信息和注册时间
- 套餐选择功能（30天/90天/365天）
- 购买流程说明
- 功能特性展示网格
- 响应式设计，支持移动端
- 动画效果和交互反馈

💬 客服联系功能：

- 支持多种联系方式：QQ、微信、电话、邮箱
- 客服工作时间显示
- 配置页面：config_service.php
- 实时预览客服信息展示效果
- API接口：get_service_info.php

🔧 管理工具：

- test_days.php - 天数计算测试工具
- config_purchase.php - 购买链接配置管理
- config_service.php - 客服联系方式配置
- get_service_info.php - 客服信息获取API

📱 管理页面访问地址：

- 购买链接配置：http://*************/api/config_purchase.php
- 客服信息配置：http://*************/api/config_service.php
- 天数测试工具：http://*************/api/test_days.php

💡 注意事项：

1. 确保服务器防火墙允许HTTP访问
2. 如果使用HTTPS，需要SSL证书
3. 定期备份数据库
4. 监控API访问日志
5. 可以通过修改 system_config 表来调整系统参数

🛠️ 故障排除：

如果API无法访问：
1. 检查服务器是否运行
2. 检查PHP服务是否启动
3. 检查文件路径是否正确
4. 检查数据库连接是否正常
5. 查看服务器错误日志

📞 技术支持：

如有问题，请检查：
- 服务器错误日志
- PHP错误日志
- 数据库连接状态
- 网络连接情况
